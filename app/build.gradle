plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
}

android {
    namespace 'com.akira.manager'
    compileSdk 34

    defaultConfig {
        applicationId "com.akira.mdm"
        minSdk 26
        targetSdk 34
        versionCode 15
        versionName "1.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.5.8'
    }
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    // implementation libs.core.ktx
    def room_version = "2.6.1"

    implementation 'androidx.core:core-ktx:1.12.0'
    implementation platform('org.jetbrains.kotlin:kotlin-bom:1.9.22')
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    
    // Play Core for in-app updates and reviews
    // implementation 'com.google.android.play:core-ktx:1.10.3'
    
    // AppCompat
    implementation 'androidx.appcompat:appcompat:1.6.1'
    
    implementation platform('androidx.compose:compose-bom:2024.01.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.material:material-icons-extended'
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // Room
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    kapt "androidx.room:room-compiler:$room_version"
    
    // OkHttp3
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    
    // DocumentFile
    implementation 'androidx.documentfile:documentfile:1.0.1'
    
    // Splash Screen
    implementation 'androidx.core:core-splashscreen:1.0.1'
    
    // Additional Compose dependencies
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    implementation 'com.google.accompanist:accompanist-systemuicontroller:0.32.0'
    
    // Compose Foundation
    implementation 'androidx.compose.foundation:foundation:1.6.1'
    
    // Compose Runtime
    implementation 'androidx.compose.runtime:runtime:1.6.1'
    implementation 'androidx.compose.runtime:runtime-livedata:1.6.1'
    
    // Compose UI
    implementation 'androidx.compose.ui:ui:1.6.1'
    implementation 'androidx.compose.ui:ui-tooling:1.6.1'
    implementation 'androidx.compose.ui:ui-tooling-preview:1.6.1'
    
    // Navigation Compose
    implementation 'androidx.navigation:navigation-compose:2.7.6'
    
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation platform('androidx.compose:compose-bom:2024.01.00')
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
} 