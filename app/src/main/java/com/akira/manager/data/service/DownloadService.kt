@file:OptIn(ExperimentalStdlibApi::class, kotlin.contracts.ExperimentalContracts::class, kotlin.experimental.ExperimentalTypeInference::class)

package com.akira.manager.data.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.os.IBinder
import android.os.PowerManager
import android.os.StatFs
import android.os.storage.StorageManager
import android.webkit.MimeTypeMap
import androidx.core.app.NotificationCompat
import com.akira.manager.MainActivity
import com.akira.manager.R
import com.akira.manager.data.local.DownloadDatabase
import com.akira.manager.data.model.DownloadStatus
import com.akira.manager.data.repository.DownloadRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import android.widget.Toast
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager

class DownloadService : Service() {
    private val activeDownloads = ConcurrentHashMap<Long, DownloadTask>()
    private val downloadQueue = ConcurrentHashMap<Long, Triple<String, String, String>>() // id -> (url, fileName, mimeType)
    private var wakeLock: PowerManager.WakeLock? = null
    private lateinit var repository: DownloadRepository
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activeDownloadCount = AtomicInteger(0)
    private lateinit var notificationManager: NotificationManager

    companion object {
        const val EXTRA_DOWNLOAD_ID = "download_id"
        const val EXTRA_URL = "url"
        const val EXTRA_FILE_NAME = "file_name"
        const val EXTRA_MIME_TYPE = "mime_type"
        const val EXTRA_SOURCE = "source"
        const val ACTION_PAUSE_DOWNLOAD = "com.akira.manager.action.PAUSE_DOWNLOAD"
        const val ACTION_RESUME_DOWNLOAD = "com.akira.manager.action.RESUME_DOWNLOAD"
        const val ACTION_CANCEL_DOWNLOAD = "com.akira.manager.action.CANCEL_DOWNLOAD"
        const val ACTION_CANCEL_NOTIFICATION = "com.akira.manager.action.CANCEL_NOTIFICATION"
        private const val MAX_CONCURRENT_DOWNLOADS = 20
        private const val NOTIFICATION_ID_BASE = 1000
        private const val CHANNEL_ID = "download_channel"
        private const val CHANNEL_NAME = "Downloads"
        private const val CHANNEL_DESCRIPTION = "Download progress notifications"
        private const val BUFFER_SIZE = 8192 * 1024 // 8MB buffer
        private const val MIN_PROGRESS_INTERVAL = 500L // 500ms
        private const val SPEED_UPDATE_INTERVAL = 1000L // 1 second
        private const val DEFAULT_THREADS = 4 // Default number of threads to use
        private const val MIN_FILE_SIZE_FOR_CHUNKS = 10 * 1024 * 1024 // 10MB minimum file size for chunking
        private const val MAX_RETRIES = 3 // Maximum number of retries for failed chunks
        private const val RETRY_DELAY = 2000L // Delay between retries in milliseconds
    }

    /**
     * Represents a single chunk of a file being downloaded
     */
    private data class DownloadChunk(
        val id: Int,
        val startByte: Long,
        val endByte: Long,
        val url: String,
        val tempFile: File,
        val partialFile: File,
        var bytesDownloaded: AtomicLong = AtomicLong(0),
        var isCompleted: Boolean = false,
        var isFailed: Boolean = false,
        var error: String? = null,
        var retryCount: Int = 0
    )

    inner class DownloadTask(
        val downloadId: Long,
        val url: String,
        val fileName: String,
        val mimeType: String
    ) {
        var job: Job? = null
        val isPaused = AtomicBoolean(false)
        var bytesDownloaded: Long = 0
        private val settingsManager = com.akira.manager.data.local.SettingsManager(applicationContext)
        private val chunks = ConcurrentHashMap<Int, DownloadChunk>()
        private val progressMutex = Mutex()
        private var supportsRanges: Boolean = false
        private var totalFileSize: Long = -1
        var lastSpeedUpdate: Long = 0
        var lastBytesRead: Long = 0
        val speedSamples = mutableListOf<Float>()
        private val activeConnections = ConcurrentHashMap<Int, okhttp3.Call>()
        private val retryMutex = Mutex()
        
        init {
            val downloadItem = runBlocking {
                repository.getDownloadById(downloadId)
            }
            bytesDownloaded = downloadItem?.downloadedBytes ?: 0
            lastBytesRead = bytesDownloaded
            lastSpeedUpdate = System.currentTimeMillis()
        }

        private suspend fun getFileSizeAndCheckRangeSupport(url: String): Pair<Long, Boolean> {
            return withContext(Dispatchers.IO) {
                val client = OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .followRedirects(true)
                    .followSslRedirects(true)
                    .build()
                
                val request = Request.Builder()
                    .url(url)
                    .head()
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .build()
                
                try {
                    client.newCall(request).execute().use { response ->
                        if (!response.isSuccessful) {
                            return@withContext Pair(-1L, false)
                        }
                        
                        val acceptRanges = response.header("Accept-Ranges")
                        val supportsRanges = acceptRanges == "bytes"
                        val contentLength = response.header("Content-Length")?.toLongOrNull() ?: -1L
                        
                        <EMAIL> = supportsRanges
                        <EMAIL> = contentLength
                        
                        return@withContext Pair(contentLength, supportsRanges)
                    }
                } catch (e: Exception) {
                    android.util.Log.e("DownloadService", "Error checking range support: ${e.message}")
                    return@withContext Pair(-1L, false)
                }
            }
        }

        private fun createDownloadChunks(fileSize: Long, numThreads: Int, tempDir: File): List<DownloadChunk> {
            if (!tempDir.exists()) {
                val success = tempDir.mkdirs()
                if (!success) {
                    android.util.Log.e("DownloadService", "[DownloadTask] Failed to create chunk directory: $tempDir")
                    throw IOException("Failed to create chunk directory: $tempDir")
                }
            }
            
            val chunks = mutableListOf<DownloadChunk>()
            val chunkSize = fileSize / numThreads
            
            var remainingBytes = if (bytesDownloaded > 0) {
                bytesDownloaded
            } else {
                0L
            }
            
            for (i in 0 until numThreads) {
                val chunkStartByte = i * chunkSize
                val chunkEndByte = if (i == numThreads - 1) {
                    fileSize - 1
                } else {
                    (i + 1) * chunkSize - 1
                }
                
                val tempFile = File(tempDir, "chunk_$i")
                val partialFile = File(tempDir, "chunk_${i}_partial")
                
                var existingBytes = 0L
                if (remainingBytes > 0) {
                    val chunkBytes = chunkEndByte - chunkStartByte + 1
                    existingBytes = if (remainingBytes >= chunkBytes) {
                        chunkBytes
                    } else {
                        remainingBytes
                    }
                    remainingBytes -= existingBytes
                }
                
                if (partialFile.exists()) {
                    val actualSize = partialFile.length()
                    if (actualSize != existingBytes) {
                        existingBytes = actualSize
                    }
                } else if (tempFile.exists()) {
                    val actualSize = tempFile.length()
                    if (actualSize != existingBytes) {
                        existingBytes = actualSize
                    }
                }
                
                val chunk = DownloadChunk(
                    id = i,
                    startByte = chunkStartByte + existingBytes,
                    endByte = chunkEndByte,
                    url = url,
                    tempFile = tempFile,
                    partialFile = partialFile,
                    bytesDownloaded = AtomicLong(existingBytes)
                )
                chunks.add(chunk)
            }
            
            return chunks
        }

        private suspend fun downloadChunk(chunk: DownloadChunk) {
            return withContext(Dispatchers.IO) {
                while (chunk.retryCount < MAX_RETRIES && !chunk.isCompleted && !chunk.isFailed) {
                    var outputStream: FileOutputStream? = null
                    var inputStream: InputStream? = null
                    
                    try {
                        val client = OkHttpClient.Builder()
                            .connectTimeout(30, TimeUnit.SECONDS)
                            .readTimeout(30, TimeUnit.SECONDS)
                            .writeTimeout(30, TimeUnit.SECONDS)
                            .followRedirects(true)
                            .followSslRedirects(true)
                            .build()
                        
                        val requestBuilder = Request.Builder()
                            .url(chunk.url)
                            .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                        
                        // Calculate the actual start byte based on existing progress
                        val actualStartByte = chunk.startByte + chunk.bytesDownloaded.get()
                        if (actualStartByte <= chunk.endByte) {
                            requestBuilder.addHeader("Range", "bytes=${actualStartByte}-${chunk.endByte}")
                        } else {
                            chunk.isCompleted = true
                            return@withContext
                        }
                        
                        val request = requestBuilder.build()
                        val call = client.newCall(request)
                        activeConnections[chunk.id] = call
                        
                        call.execute().use { response ->
                            if (!response.isSuccessful) {
                                throw IOException("HTTP error: ${response.code}")
                            }
                            
                            val body = response.body ?: throw IOException("Empty response body")
                            
                            // Open the partial file in append mode if it exists and has data
                            val appendMode = chunk.partialFile.exists() && chunk.bytesDownloaded.get() > 0
                            outputStream = FileOutputStream(chunk.partialFile, appendMode)
                            inputStream = body.byteStream()
                            
                            val buffer = ByteArray(BUFFER_SIZE)
                            var read = -1
                            var totalRead = 0L
                            
                            while (inputStream?.read(buffer)?.also { read = it } != -1) {
                                if (isPaused.get()) {
                                    // On pause, just flush and sync the partial file
                                    outputStream?.let { safeOutputStream ->
                                        safeOutputStream.flush()
                                        safeOutputStream.fd.sync()
                                    }
                                    
                                    progressMutex.withLock {
                                        bytesDownloaded = chunks.values.toList().sumOf { it.bytesDownloaded.get() }
                                        repository.updateDownloadProgress(downloadId, bytesDownloaded, totalFileSize)
                                    }
                                    activeConnections.remove(chunk.id)
                                    return@withContext
                                }
                                
                                if (!isActive) break
                                
                                outputStream?.let { safeOutputStream ->
                                    safeOutputStream.write(buffer, 0, read)
                                    safeOutputStream.flush()
                                    totalRead += read
                                    chunk.bytesDownloaded.addAndGet(read.toLong())
                                }
                                
                                progressMutex.withLock {
                                    bytesDownloaded = chunks.values.toList().sumOf { it.bytesDownloaded.get() }
                                }
                            }
                            
                            // Ensure all data is written to the partial file
                            outputStream?.let { safeOutputStream ->
                                safeOutputStream.flush()
                                safeOutputStream.fd.sync()
                            }
                            
                            // Verify the downloaded size matches what we expect
                            val downloadedSize = chunk.bytesDownloaded.get()
                            val expectedSize = chunk.endByte - chunk.startByte + 1
                            
                            if (downloadedSize == expectedSize) {
                                // Only when the chunk is fully downloaded, move it to the final location
                                try {
                                    // Close streams before moving file
                                    outputStream?.close()
                                    outputStream = null
                                    inputStream?.close()
                                    inputStream = null
                                    
                                    // Delete existing temp file if it exists
                                    if (chunk.tempFile.exists()) {
                                        chunk.tempFile.delete()
                                    }
                                    
                                    // Move partial to final
                                    if (!chunk.partialFile.renameTo(chunk.tempFile)) {
                                        throw IOException("Failed to move completed chunk to final location")
                                    }
                                    
                                    chunk.isCompleted = true
                                    activeConnections.remove(chunk.id)
                                } catch (e: Exception) {
                                    android.util.Log.e("DownloadService", "Error finalizing chunk ${chunk.id}: ${e.message}")
                                    chunk.retryCount++
                                    if (chunk.retryCount >= MAX_RETRIES) {
                                        chunk.isFailed = true
                                        chunk.error = e.message
                                    } else {
                                        // Still have retries left, continue with next attempt
                                    }
                                }
                            } else {
                                android.util.Log.e("DownloadService", "Chunk ${chunk.id} size mismatch: $downloadedSize != $expectedSize")
                                chunk.retryCount++
                                if (chunk.retryCount >= MAX_RETRIES) {
                                    chunk.isFailed = true
                                    chunk.error = "Size mismatch after download"
                                } else {
                                    // Still have retries left, continue with next attempt
                                }
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("DownloadService", "[DownloadTask] Error downloading chunk ${chunk.id}: ${e.message}")
                        chunk.retryCount++
                        
                        if (chunk.retryCount >= MAX_RETRIES) {
                            chunk.isFailed = true
                            chunk.error = e.message
                            activeConnections.remove(chunk.id)
                            throw e
                        }
                        
                        delay(RETRY_DELAY)
                    } finally {
                        try {
                            inputStream?.close()
                        } catch (e: Exception) {
                            android.util.Log.e("DownloadService", "Error closing input stream: ${e.message}")
                        }
                        try {
                            outputStream?.close()
                        } catch (e: Exception) {
                            android.util.Log.e("DownloadService", "Error closing output stream: ${e.message}")
                        }
                    }
                }
            }
        }

        private suspend fun combineChunks(chunks: List<DownloadChunk>, outputFile: File): Boolean {
            return withContext(Dispatchers.IO) {
                var outputStream: OutputStream? = null
                var tempOutputFile: File? = null
                
                try {
                    val sortedChunks = chunks.sortedBy { it.startByte }
                    
                    // Verify all chunks are present and have correct sizes
                    for (chunk in sortedChunks) {
                        if (!chunk.tempFile.exists()) {
                            android.util.Log.e("DownloadService", "Chunk file missing: ${chunk.tempFile}")
                            return@withContext false
                        }
                        val expectedSize = chunk.endByte - chunk.startByte + 1
                        val actualSize = chunk.tempFile.length()
                        if (actualSize != expectedSize) {
                            android.util.Log.e("DownloadService", "Chunk ${chunk.id} size mismatch: expected $expectedSize, got $actualSize")
                            return@withContext false
                        }
                    }
                    
                    // Create a temporary output file
                    tempOutputFile = File(outputFile.parentFile, "${outputFile.name}.tmp")
                    
                    outputStream = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        val uri = repository.getDownloadById(downloadId)?.localUri
                            ?: throw IOException("Invalid download URI")
                        contentResolver.openOutputStream(uri)
                            ?: throw IOException("Failed to open output stream")
                    } else {
                        FileOutputStream(tempOutputFile)
                    }
                    
                    outputStream.use { output ->
                        for (chunk in sortedChunks) {
                            try {
                                chunk.tempFile.inputStream().use { input ->
                                    val buffer = ByteArray(BUFFER_SIZE)
                                    var read: Int
                                    var totalRead = 0L
                                    
                                    while (input.read(buffer).also { read = it } != -1) {
                                        output.write(buffer, 0, read)
                                        totalRead += read
                                    }
                                    
                                    // Verify chunk was fully written
                                    val expectedSize = chunk.endByte - chunk.startByte + 1
                                    if (totalRead != expectedSize) {
                                        throw IOException("Chunk ${chunk.id} write size mismatch: expected $expectedSize, got $totalRead")
                                    }
                                }
                            } catch (e: Exception) {
                                android.util.Log.e("DownloadService", "Error writing chunk ${chunk.id}: ${e.message}")
                                throw e
                            }
                        }
                        
                        // Ensure all data is written to disk
                        output.flush()
                        if (output is FileOutputStream) {
                            output.fd.sync()
                        }
                    }
                    
                    // For non-Android Q, move the temporary file to the final location
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q && tempOutputFile != null) {
                        if (outputFile.exists()) {
                            outputFile.delete()
                        }
                        if (!tempOutputFile.renameTo(outputFile)) {
                            throw IOException("Failed to move temporary output file")
                        }
                    }
                    
                    // If we get here, the combination was successful
                    // Provide haptic feedback for successful download
                    withContext(Dispatchers.Main) {
                        provideHapticFeedback()
                    }
                    
                    return@withContext true
                } catch (e: Exception) {
                    android.util.Log.e("DownloadService", "Error combining chunks: ${e.message}")
                    // Clean up temporary file if it exists
                    tempOutputFile?.delete()
                    return@withContext false
                } finally {
                    try {
                        outputStream?.close()
                    } catch (e: Exception) {
                        android.util.Log.e("DownloadService", "Error closing output stream: ${e.message}")
                    }
                }
            }
        }

        fun start() {
            if (job?.isActive == true) {
                if (isPaused.get()) {
                    resume()
                }
                return
            }
            
            isPaused.set(false)
            
            // Ensure we set the status to DOWNLOADING before starting the job
            serviceScope.launch {
                repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
            }
            
            job = serviceScope.launch {
                activeDownloadCount.incrementAndGet()
                try {
                    repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING) // Ensure status is set to DOWNLOADING
                    val (fileSize, supportsRanges) = getFileSizeAndCheckRangeSupport(url)
                    
                    if (fileSize <= 0) {
                        updateNotification(downloadId, 0, 0, 0f, true)
                    } else {
                        updateNotification(downloadId, 0, fileSize, 0f, false)
                    }
                    
                    val customPath = settingsManager.getCustomDownloadPath()
                    val downloadDir: File
                    val usingContentUri = customPath.startsWith("content://")
                    
                    if (customPath.isNotEmpty() && usingContentUri) {
                        val uri = Uri.parse(customPath)
                        val docFile = androidx.documentfile.provider.DocumentFile.fromTreeUri(applicationContext, uri)
                        
                        if (docFile == null || !docFile.exists()) {
                            downloadDir = File(
                                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                                "MDM"
                            )
                        } else {
                            downloadDir = File(
                                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                                "MDM"
                            )
                        }
                    } else if (customPath.isNotEmpty()) {
                        downloadDir = File(customPath)
                        if (!downloadDir.exists()) {
                            val success = downloadDir.mkdirs()
                            if (!success) {
                                throw IOException("Failed to create download directory: $customPath")
                            }
                        }
                    } else {
                        downloadDir = File(
                            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                            "MDM"
                        )
                        if (!downloadDir.exists()) {
                            val success = downloadDir.mkdirs()
                            if (!success) {
                                throw IOException("Failed to create default download directory")
                            }
                        }
                    }
                    
                    val file = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        if (usingContentUri) {
                            val uri = Uri.parse(customPath)
                            
                            try {
                                val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                                applicationContext.contentResolver.takePersistableUriPermission(uri, takeFlags)
                                
                                val docFile = androidx.documentfile.provider.DocumentFile.fromTreeUri(applicationContext, uri)
                                
                                if (docFile != null && docFile.exists()) {
                                    var targetFileName = fileName
                                    var counter = 1
                                    var newFile = docFile.findFile(targetFileName)
                                    
                                    while (newFile != null && newFile.exists()) {
                                        val extension = targetFileName.substringAfterLast('.', "")
                                        val baseName = if (extension.isNotEmpty()) {
                                            targetFileName.substringBeforeLast('.')
                                        } else {
                                            targetFileName
                                        }
                                        
                                        targetFileName = if (extension.isNotEmpty()) {
                                            "${baseName}_${counter}.${extension}" 
                                        } else {
                                            "${baseName}_${counter}"
                                        }
                                        
                                        counter++
                                        newFile = docFile.findFile(targetFileName)
                                    }
                                    
                                    newFile = docFile.createFile(mimeType, targetFileName)
                                    if (newFile == null) {
                                        android.util.Log.e("DownloadService", "Failed to create file in document tree")
                                        throw IOException("Failed to create file in selected directory")
                                    }
                                    
                                    repository.updateDownloadUri(downloadId, newFile.uri)
                                    
                                    val cacheDir = File(
                                        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                                        "MDM/Cache"
                                    )
                                    if (!cacheDir.exists()) {
                                        val success = cacheDir.mkdirs()
                                        if (!success) {
                                            throw IOException("Failed to create cache directory: $cacheDir")
                                        }
                                    }
                                    
                                    File(cacheDir, targetFileName)
                                } else {
                                    android.util.Log.e("DownloadService", "Document tree is null or doesn't exist: $uri")
                                    throw IOException("Selected directory not accessible: $uri")
                                }
                            } catch (e: SecurityException) {
                                android.util.Log.e("DownloadService", "Security exception accessing document tree: ${e.message}")
                                val contentValues = android.content.ContentValues().apply {
                                    put(android.provider.MediaStore.Downloads.DISPLAY_NAME, fileName)
                                    put(android.provider.MediaStore.Downloads.MIME_TYPE, mimeType)
                                    put(android.provider.MediaStore.Downloads.RELATIVE_PATH, "Download/MDM")
                                    put(android.provider.MediaStore.Downloads.IS_PENDING, 1)
                                }
                                
                                val uri = contentResolver.insert(
                                    android.provider.MediaStore.Downloads.EXTERNAL_CONTENT_URI,
                                    contentValues
                                ) ?: throw IOException("Failed to create file")
                                
                                repository.updateDownloadUri(downloadId, uri)
                                
                                val cacheDir = File(
                                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                                    "MDM/Cache"
                                )
                                if (!cacheDir.exists()) {
                                    val success = cacheDir.mkdirs()
                                    if (!success) {
                                        throw IOException("Failed to create cache directory: $cacheDir")
                                    }
                                }
                                
                                File(cacheDir, fileName)
                            }
                        } else {
                            val contentValues = android.content.ContentValues().apply {
                                put(android.provider.MediaStore.Downloads.DISPLAY_NAME, fileName)
                                put(android.provider.MediaStore.Downloads.MIME_TYPE, mimeType)
                                val relativePath = if (customPath.isNotEmpty()) {
                                    if (customPath.contains("/Download/")) {
                                        val pathAfterDownload = customPath.substringAfter("/Download/")
                                        "Download/$pathAfterDownload"
                                    } else if (customPath.contains("\\Download\\")) {
                                        val pathAfterDownload = customPath.substringAfter("\\Download\\")
                                        "Download/${pathAfterDownload.replace("\\", "/")}"
                                    } else {
                                        "Download/${customPath.substringAfterLast("/").substringAfterLast("\\")}" 
                                    }
                                } else {
                                    "Download/MDM"
                                }
                                put(android.provider.MediaStore.Downloads.RELATIVE_PATH, relativePath)
                                put(android.provider.MediaStore.Downloads.IS_PENDING, 1)
                            }
                            
                            val uri = contentResolver.insert(
                                android.provider.MediaStore.Downloads.EXTERNAL_CONTENT_URI,
                                contentValues
                            ) ?: throw IOException("Failed to create file")
                            
                            repository.updateDownloadUri(downloadId, uri)
                            
                            val cacheDir = File(
                                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                                "MDM/Cache"
                            )
                            if (!cacheDir.exists()) {
                                val success = cacheDir.mkdirs()
                                if (!success) {
                                    throw IOException("Failed to create cache directory: $cacheDir")
                                }
                            }
                            
                            File(cacheDir, fileName)
                        }
                    } else {
                        val cacheDir = File(
                            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                            "MDM/Cache"
                        )
                        if (!cacheDir.exists()) {
                            val success = cacheDir.mkdirs()
                            if (!success) {
                                throw IOException("Failed to create cache directory: $cacheDir")
                            }
                        }
                        
                        File(cacheDir, fileName)
                    }
                    
                    val resuming = bytesDownloaded > 0 && file.exists() && file.length() > 0
                    val useMultiPartDownload = supportsRanges && 
                                              fileSize > MIN_FILE_SIZE_FOR_CHUNKS && 
                                              settingsManager.getMultiPartDownloadEnabled()
                    
                    // Show toast message if server doesn't support resumable downloads
                    if (!supportsRanges) {
                        withContext(Dispatchers.Main) {
                            Toast.makeText(
                                applicationContext, 
                                "Server doesn't support resuming downloads", 
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    } else if (!useMultiPartDownload && fileSize > MIN_FILE_SIZE_FOR_CHUNKS) {
                        withContext(Dispatchers.Main) {
                            Toast.makeText(
                                applicationContext,
                                "Server doesn't support multi-thread downloads",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                    
                    if (useMultiPartDownload) {
                        val tempDir = File(
                            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                            "MDM/Cache"
                        )
                        if (!tempDir.exists()) {
                            val success = tempDir.mkdirs()
                            if (!success) {
                                throw IOException("Failed to create cache directory: $tempDir")
                            }
                        }
                        
                        val downloadCacheDir = File(tempDir, "chunks_$downloadId")
                        if (!downloadCacheDir.exists()) {
                            val success = downloadCacheDir.mkdirs()
                            if (!success) {
                                throw IOException("Failed to create download cache directory: $downloadCacheDir")
                            }
                        }
                        
                        val numThreads = when {
                            fileSize > 100 * 1024 * 1024 -> DEFAULT_THREADS
                            fileSize > 50 * 1024 * 1024 -> 3
                            fileSize > 20 * 1024 * 1024 -> 2
                            else -> 1
                        }
                        
                        val downloadChunks = createDownloadChunks(fileSize, numThreads, downloadCacheDir)
                        chunks.clear()
                        downloadChunks.forEach { chunk ->
                            chunks[chunk.id] = chunk
                        }
                        
                        repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
                        
                        var lastSpeedUpdate = 0L
                        val speedSamples = mutableListOf<Float>()
                        var lastBytesRead = 0L
                        
                        val progressJob = launch {
                            while (isActive) {
                                delay(MIN_PROGRESS_INTERVAL)
                                
                                val currentTime = System.currentTimeMillis()
                                val timeSinceLastSpeed = currentTime - lastSpeedUpdate
                                
                                val currentBytesDownloaded = chunks.values.toList().sumOf { it.bytesDownloaded.get() }
                                bytesDownloaded = currentBytesDownloaded
                                
                                if (timeSinceLastSpeed >= SPEED_UPDATE_INTERVAL) {
                                    val bytesDelta = currentBytesDownloaded - lastBytesRead
                                    val speed = bytesDelta / (timeSinceLastSpeed / 1000f)
                                    speedSamples.add(speed)
                                    if (speedSamples.size > 3) speedSamples.removeAt(0)
                                    val averageSpeed = speedSamples.average().toFloat()
                                    
                                    repository.updateDownloadProgress(downloadId, currentBytesDownloaded, fileSize)
                                    updateNotification(downloadId, currentBytesDownloaded, fileSize, averageSpeed)
                                    
                                    lastSpeedUpdate = currentTime
                                    lastBytesRead = currentBytesDownloaded
                                }
                            }
                        }
                        
                        try {
                            val chunkJobs = downloadChunks.map { chunk ->
                                launch {
                                    downloadChunk(chunk)
                                }
                            }
                            
                            chunkJobs.joinAll()
                            progressJob.cancel()
                            
                            val allCompleted = chunks.values.all { it.isCompleted && !it.isFailed }
                            if (!allCompleted) {
                                val failedChunks = chunks.values.filter { it.isFailed }
                                throw IOException("Failed to download ${failedChunks.size} chunks")
                            }
                            
                            val combined = combineChunks(chunks.values.toList(), file)
                            if (!combined) {
                                throw IOException("Failed to combine chunks")
                            }
                            
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                val uri = repository.getDownloadById(downloadId)?.localUri
                                if (uri != null) {
                                    val values = android.content.ContentValues().apply {
                                        put(android.provider.MediaStore.Downloads.IS_PENDING, 0)
                                    }
                                    try {
                                        contentResolver.update(uri, values, null, null)
                                    } catch (e: Exception) {
                                        android.util.Log.e("DownloadService", "Error updating file status: ${e.message}")
                                    }
                                }
                            }
                            
                            repository.updateDownloadProgress(downloadId, fileSize, fileSize)
                            repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED)
                            showCompletedNotification(downloadId, fileName)
                            settingsManager.incrementCompletedDownloadsCount()
                            
                            // Clean up cache directory after successful download
                            try {
                                // Delete individual chunk files
                                chunks.values.forEach { chunk ->
                                    if (chunk.tempFile.exists()) {
                                        chunk.tempFile.delete()
                                    }
                                    if (chunk.partialFile.exists()) {
                                        chunk.partialFile.delete()
                                    }
                                }
                                
                                // Delete the download cache directory
                                if (downloadCacheDir.exists()) {
                                    downloadCacheDir.deleteRecursively()
                                }
                            } catch (cleanupError: Exception) {
                                android.util.Log.e("DownloadService", "Error cleaning up cache files: ${cleanupError.message}")
                                // Non-fatal error, don't throw
                            }
                        } catch (e: Exception) {
                            chunks.values.forEach { chunk ->
                                if (chunk.tempFile.exists()) {
                                    chunk.tempFile.delete()
                                }
                            }
                            throw e
                        }
                    } else {
                        val client = OkHttpClient.Builder()
                            .connectTimeout(30, TimeUnit.SECONDS)
                            .readTimeout(30, TimeUnit.SECONDS)
                            .writeTimeout(30, TimeUnit.SECONDS)
                            .followRedirects(true)
                            .followSslRedirects(true)
                            .build()

                        val requestBuilder = Request.Builder()
                            .url(url)
                            .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                            .addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                            .addHeader("Accept-Language", "en-US,en;q=0.9")
                            .addHeader("Accept-Encoding", "gzip, deflate, br")
                            .addHeader("Connection", "keep-alive")
                            .addHeader("Sec-Ch-Ua", "\"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"")
                            .addHeader("Sec-Ch-Ua-Mobile", "?0")
                            .addHeader("Sec-Ch-Ua-Platform", "\"Windows\"")
                            .addHeader("Sec-Fetch-Dest", "document")
                            .addHeader("Sec-Fetch-Mode", "navigate")
                            .addHeader("Sec-Fetch-Site", "none")
                            .addHeader("Sec-Fetch-User", "?1")
                            .addHeader("Upgrade-Insecure-Requests", "1")
                            .addHeader("Cache-Control", "max-age=0")
                            .addHeader("DNT", "1")
                        
                        // Only add Range header if server supports it
                        if (supportsRanges && resuming) {
                            requestBuilder.addHeader("Range", "bytes=$bytesDownloaded-")
                        }

                        val request = requestBuilder.build()
                        
                        client.newCall(request).execute().use { response ->
                            if (!response.isSuccessful) {
                                if (response.code == 416) {
                                    repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED)
                                    throw IOException("Download already completed")
                                } else {
                                    throw IOException("Failed to download: ${response.code}")
                                }
                            }

                            val body = response.body ?: throw IOException("Empty response body")
                            val contentLength = if (supportsRanges && resuming) {
                                body.contentLength() + bytesDownloaded 
                            } else {
                                body.contentLength()
                            }
                            
                            var lastProgressUpdate = 0L
                            var lastSpeedUpdate = 0L
                            val speedSamples = mutableListOf<Float>()
                            var lastBytesRead = bytesDownloaded

                            withContext(Dispatchers.IO) {
                                try {
                                    val outputStream = if (usingContentUri) {
                                        val uri = repository.getDownloadById(downloadId)?.localUri
                                            ?: throw IOException("Invalid download URI")

                                        try {
                                            contentResolver.openOutputStream(uri)
                                                ?: throw IOException("Failed to open output stream for URI")
                                        } catch (e: Exception) {
                                            android.util.Log.e("DownloadService", "Error opening output stream: ${e.message}")
                                            throw IOException("Error accessing document: ${e.message}")
                                        }
                                    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                        val uri = repository.getDownloadById(downloadId)?.localUri
                                            ?: throw IOException("Invalid download URI")
                                        contentResolver.openOutputStream(uri, if (resuming) "wa" else "w")
                                            ?: throw IOException("Failed to open output stream")
                                    } else {
                                        if (resuming) {
                                            FileOutputStream(file, true)
                                        } else {
                                            FileOutputStream(file)
                                        }
                                    }
                                    
                                    outputStream.use { output ->
                                        body.byteStream().use { inputStream ->
                                            val buffer = ByteArray(BUFFER_SIZE)
                                            var read: Int

                                            while (inputStream.read(buffer).also { read = it } != -1) {
                                                if (isPaused.get()) {
                                                    repository.updateDownloadProgress(downloadId, bytesDownloaded, contentLength)
                                                    return@withContext
                                                }
                                                
                                                if (!isActive) break

                                                output.write(buffer, 0, read)
                                                bytesDownloaded += read

                                                val currentTime = System.currentTimeMillis()
                                                val timeSinceLastProgress = currentTime - lastProgressUpdate
                                                val timeSinceLastSpeed = currentTime - lastSpeedUpdate

                                                if (timeSinceLastSpeed >= SPEED_UPDATE_INTERVAL) {
                                                    val bytesDelta = bytesDownloaded - lastBytesRead
                                                    val speed = bytesDelta / (timeSinceLastSpeed / 1000f)
                                                    speedSamples.add(speed)
                                                    if (speedSamples.size > 3) speedSamples.removeAt(0)
                                                    val averageSpeed = speedSamples.average().toFloat()
                                                    
                                                    lastSpeedUpdate = currentTime
                                                    lastBytesRead = bytesDownloaded

                                                    if (timeSinceLastProgress >= MIN_PROGRESS_INTERVAL) {
                                                        repository.updateDownloadProgress(downloadId, bytesDownloaded, contentLength)
                                                        updateNotification(downloadId, bytesDownloaded, contentLength, averageSpeed)
                                                        lastProgressUpdate = currentTime
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                        val uri = repository.getDownloadById(downloadId)?.localUri
                                        if (uri != null) {
                                            val values = android.content.ContentValues().apply {
                                                put(android.provider.MediaStore.Downloads.IS_PENDING, 0)
                                            }
                                            try {
                                                contentResolver.update(uri, values, null, null)
                                            } catch (e: Exception) {
                                                android.util.Log.e("DownloadService", "Error updating file status: ${e.message}")
                                            }
                                        }
                                    }
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                    throw IOException("File access error: ${e.message}")
                                }
                            }

                            if (bytesDownloaded > 0) {
                                repository.updateDownloadProgress(downloadId, bytesDownloaded, contentLength)
                                repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED)
                                showCompletedNotification(downloadId, fileName)
                                settingsManager.incrementCompletedDownloadsCount()
                            } else {
                                repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                                repository.updateDownloadError(downloadId, "Download failed: No content downloaded")
                                updateNotification(downloadId, 0, 0, 0f)
                            }
                        }
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                    if (!isPaused.get()) {
                        repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                        repository.updateDownloadError(downloadId, "Download failed: ${e.message}")
                        updateNotification(downloadId, bytesDownloaded, 0, 0f)
                    }
                } catch (e: CancellationException) {
                    if (isPaused.get()) {
                        repository.updateDownloadStatus(downloadId, DownloadStatus.PAUSED)
                    } else {
                        throw e
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    if (!isPaused.get()) {
                        repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                        repository.updateDownloadError(downloadId, "Download failed: ${e.message}")
                        updateNotification(downloadId, bytesDownloaded, 0, 0f)
                    }
                } finally {
                    activeDownloadCount.decrementAndGet()
                    if (!isPaused.get()) {
                        activeDownloads.remove(downloadId)
                        if (activeDownloadCount.get() == 0 && downloadQueue.isEmpty()) {
                            stopSelf()
                        }
                    }
                }
            }
        }
        
        fun pause() {
            isPaused.set(true)
            activeConnections.values.forEach { call ->
                if (!call.isCanceled()) {
                    call.cancel()
                }
            }
            activeConnections.clear()
        }
        
        fun resume() {
            isPaused.set(false)
        }
        
        fun cancel() {
            job?.cancel()
            job = null
            activeDownloads.remove(downloadId)
            serviceScope.launch {
                repository.updateDownloadStatus(downloadId, DownloadStatus.CANCELLED)
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        repository = DownloadRepository(DownloadDatabase.getDatabase(this).downloadDao(), this)
        notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        acquireWakeLock()
        
        // Start queue processor
        serviceScope.launch {
            processDownloadQueue()
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (intent == null) return START_NOT_STICKY

        when (intent.action) {
            ACTION_PAUSE_DOWNLOAD -> {
                val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
                if (downloadId != -1L) {
                    activeDownloads[downloadId]?.let { task ->
                        task.pause()
                        serviceScope.launch {
                            repository.updateDownloadStatus(downloadId, DownloadStatus.PAUSED)
                            // Clear speed when paused to prevent "Calculating..." state
                            repository.updateDownloadSpeed(downloadId, 0f)
                            // Clear the notification when paused
                            cancelNotification(downloadId)
                        }
                    }
                }
            }
            ACTION_RESUME_DOWNLOAD -> {
                val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
                if (downloadId != -1L) {
                    activeDownloads[downloadId]?.let { task ->
                        task.resume()
                        serviceScope.launch {
                            repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
                            // Reset speed tracking variables
                            task.lastSpeedUpdate = System.currentTimeMillis()
                            task.lastBytesRead = task.bytesDownloaded
                            task.speedSamples.clear()
                        }
                        // If job is null or not active, restart the download
                        if (task.job == null || !task.job!!.isActive) {
                            task.start()
                        }
                    }
                }
            }
            ACTION_CANCEL_DOWNLOAD -> {
                val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
                if (downloadId > 0) {
                    activeDownloads[downloadId]?.cancel()
                }
                return START_NOT_STICKY
            }
            ACTION_CANCEL_NOTIFICATION -> {
                val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
                if (downloadId > 0) {
                    cancelNotification(downloadId)
                }
                return START_NOT_STICKY
            }
        }

        var url = intent.getStringExtra(EXTRA_URL)
        var fileName = intent.getStringExtra(EXTRA_FILE_NAME)
        var mimeType = intent.getStringExtra(EXTRA_MIME_TYPE) ?: "application/octet-stream"
        val source = intent.getStringExtra(EXTRA_SOURCE) ?: "browser"
        val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)

        // Special handling for content:// and file:// URIs from Firefox
        if ((url?.startsWith("content://") == true || url?.startsWith("file://") == true) && source == "firefox") {
            serviceScope.launch {
                handleContentUriDownload(downloadId, url, fileName ?: "download", mimeType)
            }
            return START_NOT_STICKY
        }

        if (downloadId > 0) {
            // Check if download is already active
            if (activeDownloads.containsKey(downloadId)) {
                return START_NOT_STICKY
            }
            
            // Check if download is already queued
            if (downloadQueue.containsKey(downloadId)) {
                return START_NOT_STICKY
            }
            
            // Start the download directly
            startDownload(downloadId, url ?: "", fileName ?: "download", mimeType)
            return START_NOT_STICKY
        }
        
        // For new downloads, create a download item and add to queue
        serviceScope.launch {
            val newDownloadId = repository.addDownload(
                url = url ?: "",
                fileName = fileName ?: "download",
                mimeType = mimeType,
                source = source
            )
            if (newDownloadId > 0) {
                // Add to queue if we have too many active downloads
                if (activeDownloadCount.get() >= MAX_CONCURRENT_DOWNLOADS) {
                    downloadQueue[newDownloadId] = Triple(url ?: "", fileName ?: "download", mimeType)
                } else {
                    startDownload(newDownloadId, url ?: "", fileName ?: "download", mimeType)
                }
            }
        }

        return START_STICKY
    }

    private suspend fun processDownloadQueue() {
        while (true) {
            try {
                // Process queue if we have capacity and there are queued downloads
                while (activeDownloadCount.get() < MAX_CONCURRENT_DOWNLOADS && downloadQueue.isNotEmpty()) {
                    val (downloadId, downloadInfo) = downloadQueue.entries.firstOrNull() ?: break
                    downloadQueue.remove(downloadId)
                    
                    val (url, fileName, mimeType) = downloadInfo
                    startDownload(downloadId, url, fileName, mimeType)
                }
                delay(100) // Small delay to prevent busy waiting
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun startDownload(downloadId: Long, url: String, fileName: String, mimeType: String) {
        if (activeDownloads.containsKey(downloadId)) {
            // Already active, try to resume if paused
            val task = activeDownloads[downloadId]
            task?.resume()
            return
        }
        
        // Set status to DOWNLOADING immediately to update the UI
        serviceScope.launch {
            repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
        }
        
        // Check available storage space and enhance extension detection before starting download
        serviceScope.launch {
            try {
                val client = OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .followRedirects(true)
                    .followSslRedirects(true)
                    .build()
                
                val request = Request.Builder()
                    .url(url)
                    .head()
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .build()
                
                var fileSize = -1L
                var detectedMimeType = mimeType
                var enhancedFileName = fileName
                
                try {
                    client.newCall(request).execute().use { response ->
                        if (response.isSuccessful) {
                            fileSize = response.header("Content-Length")?.toLongOrNull() ?: -1L
                            
                            // Get Content-Type if available and original mime type is generic
                            if (mimeType == "application/octet-stream") {
                                response.header("Content-Type")?.let { contentType ->
                                    // Extract base mime type without parameters
                                    val baseContentType = contentType.split(";").first().trim()
                                    if (baseContentType.isNotBlank()) {
                                        detectedMimeType = baseContentType
                                        android.util.Log.d("DownloadService", "Updated MIME type from HEAD: $baseContentType")
                                    }
                                }
                            }
                            
                            // Check for Content-Disposition header for filename
                            response.header("Content-Disposition")?.let { contentDisposition ->
                                // Try different patterns for Content-Disposition
                                val patterns = listOf(
                                    "filename=[\"]?([^\"]*)",  // Standard format
                                    "filename\\*=UTF-8''([^;]*)",  // RFC 5987 encoded
                                    "filename\\*=([^;]*)",  // RFC 5987 without charset
                                    "name=[\"]?([^\"]*)"  // Alternative format
                                )
                                
                                for (pattern in patterns) {
                                    val matcher = java.util.regex.Pattern.compile(pattern).matcher(contentDisposition)
                                    if (matcher.find()) {
                                        val extractedFileName = matcher.group(1)
                                        
                                        // Decode if URL encoded
                                        try {
                                            val decodedFileName = java.net.URLDecoder.decode(extractedFileName, "UTF-8")
                                            if (decodedFileName.isNotBlank()) {
                                                enhancedFileName = decodedFileName
                                                android.util.Log.d("DownloadService", "Found filename in Content-Disposition: $decodedFileName")
                                                break
                                            }
                                        } catch (e: Exception) {
                                            // If decoding fails, use the original
                                            if (extractedFileName.isNotBlank()) {
                                                enhancedFileName = extractedFileName
                                                android.util.Log.d("DownloadService", "Found filename (not decoded) in Content-Disposition: $extractedFileName")
                                                break
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("DownloadService", "Error during HEAD request: ${e.message}")
                    // Continue with download even if HEAD request fails
                }

                // Check for storage space if we got file size info
                if (fileSize > 0) {
                    val hasEnoughSpace = checkStorageSpace(fileSize)
                    if (!hasEnoughSpace) {
                        repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                        repository.updateDownloadError(downloadId, "Insufficient storage space")
                        updateNotification(downloadId, 0, 0, 0f)
                        return@launch
                    }
                }
                
                // Update file name in database if we found a better one
                if (enhancedFileName != fileName) {
                    repository.updateDownloadFileName(downloadId, enhancedFileName)
                }
                
                // Update MIME type in database if we found a better one
                if (detectedMimeType != mimeType) {
                    repository.updateDownloadMimeType(downloadId, detectedMimeType)
                }
                
                // Create new download task and start it
                val task = DownloadTask(downloadId, url, enhancedFileName, detectedMimeType)
                activeDownloads[downloadId] = task
                task.start()
            } catch (e: Exception) {
                repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                repository.updateDownloadError(downloadId, "Failed to check storage space: ${e.message}")
                updateNotification(downloadId, 0, 0, 0f)
            }
        }
    }

    private fun checkStorageSpace(requiredBytes: Long): Boolean {
        val storageManager = getSystemService(Context.STORAGE_SERVICE) as StorageManager
        
        // Check internal storage first
        val internalStorage = Environment.getDataDirectory()
        val stat = StatFs(internalStorage.path)
        val availableBytes = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            stat.availableBytes
        } else {
            @Suppress("DEPRECATION")
            stat.availableBlocks.toLong() * stat.blockSize
        }

        // Add 10% buffer to required space for safety
        val requiredWithBuffer = (requiredBytes * 1.1).toLong()
        if (availableBytes >= requiredWithBuffer) {
            return true
        }

        // Check external storage if available
        val externalStorage = Environment.getExternalStorageDirectory()
        if (externalStorage != null && externalStorage.exists()) {
            val externalStat = StatFs(externalStorage.path)
            val externalAvailableBytes = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                externalStat.availableBytes
            } else {
                @Suppress("DEPRECATION")
                externalStat.availableBlocks.toLong() * externalStat.blockSize
            }

            if (externalAvailableBytes >= requiredWithBuffer) {
                return true
            }
        }

        return false
    }
    
    private fun pauseDownload(downloadId: Long) {
        activeDownloads[downloadId]?.pause()
    }
    
    private fun resumeDownload(downloadId: Long) {
        val task = activeDownloads[downloadId]
        if (task != null) {
            task.resume()
        } else {
            // If task isn't in active downloads, we need to get info from the database and restart
            serviceScope.launch {
                val download = repository.getDownloadById(downloadId)
                if (download != null) {
                    // Update status to downloading
                    repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
                    
                    // Add to queue with high priority
                    downloadQueue[downloadId] = Triple(download.url, download.fileName, download.mimeType)
                    
                    // If we're under the concurrent limit, start immediately
                    if (activeDownloadCount.get() < MAX_CONCURRENT_DOWNLOADS) {
                        startDownload(downloadId, download.url, download.fileName, download.mimeType)
                    }
                }
            }
        }
    }

    private fun updateNotification(downloadId: Long, bytesDownloaded: Long, totalBytes: Long, speed: Float, indeterminate: Boolean = false) {
        val downloadTask = activeDownloads[downloadId] ?: return
        
        // Don't show notification if download is paused
        if (downloadTask.isPaused.get()) {
            cancelNotification(downloadId)
            return
        }
        
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val progress = if (totalBytes > 0 && !indeterminate) {
            ((bytesDownloaded * 100) / totalBytes).toInt()
        } else {
            0
        }

        val speedText = when {
            speed > 1024 * 1024 -> "%.1f MB/s".format(speed / (1024 * 1024))
            speed > 1024 -> "%.1f KB/s".format(speed / 1024)
            else -> "%.1f B/s".format(speed)
        }

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(downloadTask.fileName)
            .setContentText(if (indeterminate) "Preparing download..." else "$progress% • $speedText")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setProgress(100, progress, indeterminate)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID_BASE + downloadId.toInt(), notification)
    }

    private fun showCompletedNotification(downloadId: Long, fileName: String) {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        // Create notification
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Download completed")
            .setContentText(fileName)
            .setSmallIcon(R.drawable.ic_check_circle)
            .setContentIntent(pendingIntent)
            .setOngoing(false)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID_BASE + downloadId.toInt(), notification)
        
        // Show toast notification for download completion
        val handler = android.os.Handler(android.os.Looper.getMainLooper())
        handler.post {
            Toast.makeText(
                applicationContext,
                "Download completed: $fileName",
                Toast.LENGTH_SHORT
            ).show()
        }
        

        
        // Provide haptic feedback
        provideHapticFeedback()
    }


    
    /**
     * Get file name from a content URI
     */
    private fun getFileNameFromUri(uri: Uri): String {
        var fileName = "Unknown"
        
        if (uri.scheme == "content") {
            try {
                contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                    if (cursor.moveToFirst()) {
                        val displayNameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                        if (displayNameIndex != -1) {
                            fileName = cursor.getString(displayNameIndex)
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("DownloadService", "Error getting file name from URI: ${e.message}")
            }
        } else if (uri.scheme == "file") {
            fileName = uri.lastPathSegment ?: "Unknown"
        }
        
        return fileName
    }

    /**
     * Provide haptic feedback for download completion
     */
    private fun provideHapticFeedback() {
        try {
            // Get the vibrator service
            val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                val vibratorManager = getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
                vibratorManager.defaultVibrator
            } else {
                @Suppress("DEPRECATION")
                getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            }
            
            // Check if vibrator is available
            if (vibrator.hasVibrator()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    // For Android 8.0 (API 26) and above, create a pleasant vibration pattern
                    val vibrationEffect = VibrationEffect.createOneShot(
                        100, // Duration in milliseconds - short vibration
                        VibrationEffect.DEFAULT_AMPLITUDE
                    )
                    vibrator.vibrate(vibrationEffect)
                } else {
                    // For older Android versions
                    @Suppress("DEPRECATION")
                    vibrator.vibrate(100) // Duration in milliseconds
                }
            }
        } catch (e: Exception) {
            // Log error but don't crash if vibration fails
            android.util.Log.e("DownloadService", "Error providing haptic feedback: ${e.message}")
        }
    }

    private fun cancelNotification(downloadId: Long) {
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancel(NOTIFICATION_ID_BASE + downloadId.toInt())
    }

    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            CHANNEL_NAME,
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = CHANNEL_DESCRIPTION
        }
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }

    private fun acquireWakeLock() {
        val powerManager = getSystemService(POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.PARTIAL_WAKE_LOCK,
            "MaterialManager:DownloadService"
        ).apply {
            setReferenceCounted(true)
            acquire()
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        super.onDestroy()
        activeDownloads.values.forEach { it.cancel() }
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            stopForeground(STOP_FOREGROUND_REMOVE)
        } else {
            @Suppress("DEPRECATION")
            stopForeground(true)
        }
    }

    /**
     * Special handler for content:// and file:// URIs from Firefox on older Android versions
     * This function handles downloads that come directly from Firefox or other apps
     * that provide content:// or file:// URIs.
     *
     * @param downloadId Unique ID of the download
     * @param uriString The content URI string to download from
     * @param fileName Suggested file name for the download
     * @param mimeType MIME type of the content
     */
    private suspend fun handleContentUriDownload(downloadId: Long, uriString: String, fileName: String, mimeType: String) {
        // Track download progress
        var bytesRead: Int
        var totalBytesRead = 0L
        var lastUpdateTime = System.currentTimeMillis()
        var lastBytesRead = 0L
        val speedSamples = mutableListOf<Float>()
        var averageSpeed = 0f
        var targetFile: File? = null
        var outputStream: OutputStream? = null
        var inputStream: InputStream? = null
        var isCompleted = false
        val isPaused = AtomicBoolean(false)
        var uri: Uri? = null
        var outUri: Uri? = null
        
        try {
            // Parse the URI
            uri = Uri.parse(uriString)
            
            // Update status to DOWNLOADING
            repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
            
            // Create notification
            val notification = createProgressNotification(fileName, 0, true)
            startForeground(NOTIFICATION_ID_BASE + downloadId.toInt(), notification)
            
            // Process file name to ensure it's valid and has correct extension
            val processedFileName = processFileName(fileName, mimeType, uriString, null)
            
            // Get settings for download location
            val settingsManager = com.akira.manager.data.local.SettingsManager(applicationContext)
            val customPath = settingsManager.getCustomDownloadPath()
            
            // Handle file creation based on Android version and settings
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // Use MediaStore API for Android 10+
                val contentValues = android.content.ContentValues().apply {
                    put(android.provider.MediaStore.Downloads.DISPLAY_NAME, processedFileName)
                    put(android.provider.MediaStore.Downloads.MIME_TYPE, mimeType)
                    
                    // Determine relative path based on settings
                    val relativePath = if (customPath.isNotEmpty() && !customPath.startsWith("content://")) {
                        // Extract relative path from full path
                        // If it's a full path, we need to get just the part after "Download/"
                        if (customPath.contains("/Download/")) {
                            val pathAfterDownload = customPath.substringAfter("/Download/")
                            "Download/$pathAfterDownload"
                        } else if (customPath.contains("\\Download\\")) {
                            val pathAfterDownload = customPath.substringAfter("\\Download\\")
                            "Download/${pathAfterDownload.replace("\\", "/")}"
                        } else {
                            // If it's just a folder name, use it directly
                            "Download/${customPath.substringAfterLast("/").substringAfterLast("\\")}" 
                        }
                    } else {
                        "Download/MDM"
                    }
                    
                    put(android.provider.MediaStore.Downloads.RELATIVE_PATH, relativePath)
                    put(android.provider.MediaStore.Downloads.IS_PENDING, 1)
                }
                
                // Create the file in MediaStore
                outUri = contentResolver.insert(
                    android.provider.MediaStore.Downloads.EXTERNAL_CONTENT_URI,
                    contentValues
                ) ?: throw IOException("Failed to create output file in MediaStore")
                
                // Update the download with the URI
                repository.updateDownloadUri(downloadId, outUri)
                
                // Open output stream
                outputStream = contentResolver.openOutputStream(outUri)
                    ?: throw IOException("Failed to open output stream for MediaStore file")
                
                // Create a temporary File reference just for tracking
                targetFile = File(
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                    "MDM/Cache/$processedFileName"
                )
            } else {
                // Direct file access for older Android versions
                val downloadDir = if (customPath.isNotEmpty() && !customPath.startsWith("content://")) {
                    File(customPath)
                } else {
                    File(
                        Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
                        "MDM"
                    )
                }
                
                // Ensure directory exists
                if (!downloadDir.exists()) {
                    val success = downloadDir.mkdirs()
                    if (!success) {
                        throw IOException("Failed to create download directory: $downloadDir")
                    }
                }
                
                // Create file with unique name to avoid conflicts
                targetFile = File(downloadDir, processedFileName)
                var counter = 1
                
                // If file exists, add counter to name
                while (targetFile!!.exists()) {
                    val extension = processedFileName.substringAfterLast('.', "")
                    val baseName = if (extension.isNotEmpty()) {
                        processedFileName.substringBeforeLast('.')
                    } else {
                        processedFileName
                    }
                    
                    val newFileName = if (extension.isNotEmpty()) {
                        "${baseName}_${counter}.${extension}"
                    } else {
                        "${baseName}_${counter}"
                    }
                    
                    targetFile = File(downloadDir, newFileName)
                    counter++
                }
                
                // Create output stream
                outputStream = FileOutputStream(targetFile)
                
                // Create URI from file for database
                outUri = Uri.fromFile(targetFile)
                repository.updateDownloadUri(downloadId, outUri)
            }
            
            // Open input stream from content URI
            inputStream = contentResolver.openInputStream(uri)
                ?: throw IOException("Failed to open input stream from content URI")
            
            // Try to get content length for progress reporting
            var contentLength = -1L
            if (uri.scheme == "content") {
                try {
                    contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                        val sizeIndex = cursor.getColumnIndex(android.provider.OpenableColumns.SIZE)
                        if (sizeIndex != -1 && cursor.moveToFirst()) {
                            contentLength = cursor.getLong(sizeIndex)
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.w("DownloadService", "Could not determine content length: ${e.message}")
                }
            }
            
            // Update total size in database if available
            if (contentLength > 0) {
                repository.updateDownloadTotalSize(downloadId, contentLength)
            }
            
            // Set up buffer for copying
            val buffer = ByteArray(BUFFER_SIZE)
            val pauseCheckInterval = 10 * BUFFER_SIZE // Check for pause after this many bytes
            var bytesUntilPauseCheck = pauseCheckInterval
            
            // Read and write data
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                // Check if we should pause
                bytesUntilPauseCheck -= bytesRead
                if (bytesUntilPauseCheck <= 0) {
                    // Check if download has been paused
                    bytesUntilPauseCheck = pauseCheckInterval
                    val download = repository.getDownloadById(downloadId)
                    if (download?.status == DownloadStatus.PAUSED) {
                        isPaused.set(true)
                    }
                    
                    // If paused, wait until resumed
                    while (isPaused.get()) {
                        delay(500)
                        val updatedDownload = repository.getDownloadById(downloadId)
                        if (updatedDownload?.status == DownloadStatus.DOWNLOADING) {
                            isPaused.set(false)
                        } else if (updatedDownload?.status == DownloadStatus.CANCELLED) {
                            throw IOException("Download cancelled")
                        }
                    }
                }
                
                // Write data to output
                outputStream.write(buffer, 0, bytesRead)
                totalBytesRead += bytesRead
                
                // Update progress and speed calculations
                val currentTime = System.currentTimeMillis()
                val timeElapsed = currentTime - lastUpdateTime
                
                // Update speed calculation and UI every second
                if (timeElapsed >= SPEED_UPDATE_INTERVAL) {
                    val bytesDelta = totalBytesRead - lastBytesRead
                    val speed = bytesDelta / (timeElapsed / 1000f)
                    speedSamples.add(speed)
                    
                    // Keep last 3 samples for average
                    if (speedSamples.size > 3) {
                        speedSamples.removeAt(0)
                    }
                    
                    averageSpeed = speedSamples.average().toFloat()
                    
                    // Update database with progress
                    repository.updateDownloadProgress(downloadId, totalBytesRead, contentLength)
                    repository.updateDownloadSpeed(downloadId, averageSpeed)
                    
                    // Update notification
                    val progress = if (contentLength > 0) {
                        (totalBytesRead * 100 / contentLength).toInt()
                    } else {
                        -1
                    }
                    
                    val progressNotification = createProgressNotification(
                        processedFileName,
                        progress,
                        contentLength <= 0
                    )
                    notificationManager.notify(NOTIFICATION_ID_BASE + downloadId.toInt(), progressNotification)
                    
                    // Reset tracking variables
                    lastUpdateTime = currentTime
                    lastBytesRead = totalBytesRead
                }
            }
            
            // Mark as completed
            isCompleted = true
            
            // Update MediaStore on Android 10+ to mark download as complete
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && outUri != null) {
                val contentValues = android.content.ContentValues().apply {
                    put(android.provider.MediaStore.Downloads.IS_PENDING, 0)
                }
                try {
                    contentResolver.update(outUri, contentValues, null, null)
                } catch (e: Exception) {
                    android.util.Log.e("DownloadService", "Error updating file status: ${e.message}")
                }
            }
            
            // Update final status
            repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED)
            repository.updateDownloadProgress(downloadId, totalBytesRead, totalBytesRead)
            
            // Provide haptic feedback on completion
            withContext(Dispatchers.Main) {
                provideHapticFeedback()
            }
            
            // Create completion notification
            val completionNotification = createCompletionNotification(processedFileName)
            notificationManager.notify(NOTIFICATION_ID_BASE + downloadId.toInt(), completionNotification)
            
        } catch (e: Exception) {
            android.util.Log.e("DownloadService", "Error handling content URI download: ${e.message}")
            e.printStackTrace()
            
            // Clean up incomplete file if there was an error and we didn't complete
            if (!isCompleted) {
                try {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && outUri != null) {
                        contentResolver.delete(outUri, null, null)
                    } else if (targetFile != null && targetFile.exists()) {
                        targetFile.delete()
                    }
                } catch (cleanupError: Exception) {
                    android.util.Log.e("DownloadService", "Error cleaning up incomplete file: ${cleanupError.message}")
                }
            }
            
            // Update status to failed with error message
            repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
            repository.updateDownloadError(downloadId, e.message ?: "Unknown error")
            
            // Create error notification
            val errorNotification = createErrorNotification(fileName)
            notificationManager.notify(NOTIFICATION_ID_BASE + downloadId.toInt(), errorNotification)
        } finally {
            // Close streams
            try {
                inputStream?.close()
            } catch (e: Exception) {
                android.util.Log.e("DownloadService", "Error closing input stream: ${e.message}")
            }
            
            try {
                outputStream?.close()
            } catch (e: Exception) {
                android.util.Log.e("DownloadService", "Error closing output stream: ${e.message}")
            }
        }
    }
    
    /**
     * Create a progress notification for a download
     */
    private fun createProgressNotification(fileName: String, progress: Int, indeterminate: Boolean): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(fileName)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)

        if (progress >= 0) {
            builder.setProgress(100, progress, indeterminate)
            builder.setContentText("$progress%")
        } else {
            builder.setProgress(0, 0, true)
            builder.setContentText("Downloading...")
        }

        return builder.build()
    }

    /**
     * Create a completion notification for a download
     */
    private fun createCompletionNotification(fileName: String): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Download Completed")
            .setContentText(fileName)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(false)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()
    }

    /**
     * Create an error notification for a failed download
     */
    private fun createErrorNotification(fileName: String): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Download Failed")
            .setContentText(fileName)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(false)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()
    }

    /**
     * Helper methods for file name and extension processing
     */
    
    /**
     * Process a file name to ensure it has the correct extension based on MIME type
     * and is sanitized for file system compatibility
     */
    private fun processFileName(fileName: String, mimeType: String, url: String, inputStream: InputStream? = null): String {
        // First sanitize the file name to remove invalid characters
        var processedName = sanitizeFileName(fileName)
        
        // Determine the best extension using our enhanced detection logic
        val finalExtension = determineFileExtensionSync(url, mimeType, processedName, inputStream)
        
        // Get extension from file name if it exists
        val extension = getFileExtension(processedName)
        
        // If file has no extension or extension doesn't match the final extension, append the correct one
        if (extension.isBlank() || !extension.equals(finalExtension, ignoreCase = true)) {
            processedName = if (extension.isBlank()) {
                "$processedName.$finalExtension"
            } else {
                processedName.substringBeforeLast('.') + ".$finalExtension"
            }
        }
        
        return processedName
    }
    
    /**
     * Enhanced file extension detection (synchronous version) that follows the specified steps
     * to reliably determine the correct file extension without network requests
     */
    private fun determineFileExtensionSync(url: String, mimeType: String, fileName: String, inputStream: InputStream? = null): String {
        var extension = ""
        
        // Step 1: Try to get extension from the original file name
        val fileNameExtension = getFileExtension(fileName)
        if (fileNameExtension.isNotBlank()) {
            // Only use the fileName extension if it's not a "fake" extension like .php
            if (!isFakeExtension(fileNameExtension)) {
                extension = fileNameExtension
            }
        }
        
        // If we still don't have an extension, proceed with further steps
        if (extension.isBlank()) {
            // Skip Content-Disposition header check as it requires a network request
            // That part will be handled separately in the startDownload method
            
            // Step 3: Try to get extension from MIME type
            val mimeExtension = getExtensionFromMimeType(mimeType)
            if (mimeExtension.isNotBlank()) {
                extension = mimeExtension
            }
            
            // Step 4: Try to get extension from URL path
            if (extension.isBlank() || mimeType == "application/octet-stream") {
                val urlExtension = getExtensionFromUrl(url)
                if (urlExtension.isNotBlank() && !isFakeExtension(urlExtension)) {
                    extension = urlExtension
                }
            }
            
            // Step 5: Try to detect from file signature (magic bytes) as last resort
            if ((extension.isBlank() || extension == "bin") && inputStream != null) {
                val signatureExtension = detectFileTypeFromSignature(inputStream)
                if (signatureExtension.isNotBlank()) {
                    extension = signatureExtension
                }
            }
        }
        
        // Step 6: Extension sanitization & final defaults
        extension = extension.lowercase().trim('.')
        
        // If still no extension, use fallbacks based on MIME type
        if (extension.isBlank()) {
            extension = when {
                mimeType.startsWith("text/") -> "txt"
                else -> "bin"
            }
            
            // Log a warning when falling back to .bin
            if (extension == "bin") {
                android.util.Log.w("DownloadService", "Falling back to .bin extension for URL: $url with MIME: $mimeType")
            }
        }
        
        return extension
    }
    
    /**
     * Enhanced file extension detection that follows the specified steps
     * to reliably determine the correct file extension - includes content-disposition check
     */
    private suspend fun determineFileExtension(url: String, mimeType: String, fileName: String, inputStream: InputStream? = null): String {
        var extension = ""
        
        // Step 1: Try to get extension from the original file name
        val fileNameExtension = getFileExtension(fileName)
        if (fileNameExtension.isNotBlank()) {
            // Only use the fileName extension if it's not a "fake" extension like .php
            if (!isFakeExtension(fileNameExtension)) {
                extension = fileNameExtension
            }
        }
        
        // If we still don't have an extension, proceed with further steps
        if (extension.isBlank()) {
            // Step 2: Try to get extension from Content-Disposition header via HEAD request
            val contentDispExtension = getExtensionFromContentDisposition(url)
            if (contentDispExtension.isNotBlank()) {
                extension = contentDispExtension
                return extension // If we have a content-disposition filename, that's authoritative
            }
            
            // Step 3: Try to get extension from MIME type
            val mimeExtension = getExtensionFromMimeType(mimeType)
            if (mimeExtension.isNotBlank()) {
                extension = mimeExtension
            }
            
            // Step 4: Try to get extension from URL path
            if (extension.isBlank() || mimeType == "application/octet-stream") {
                val urlExtension = getExtensionFromUrl(url)
                if (urlExtension.isNotBlank() && !isFakeExtension(urlExtension)) {
                    extension = urlExtension
                }
            }
            
            // Step 5: Try to detect from file signature (magic bytes) as last resort
            if ((extension.isBlank() || extension == "bin") && inputStream != null) {
                val signatureExtension = detectFileTypeFromSignature(inputStream)
                if (signatureExtension.isNotBlank()) {
                    extension = signatureExtension
                }
            }
        }
        
        // Step 6: Extension sanitization & final defaults
        extension = extension.lowercase().trim('.')
        
        // If still no extension, use fallbacks based on MIME type
        if (extension.isBlank()) {
            extension = when {
                mimeType.startsWith("text/") -> "txt"
                else -> "bin"
            }
            
            // Log a warning when falling back to .bin
            if (extension == "bin") {
                android.util.Log.w("DownloadService", "Falling back to .bin extension for URL: $url with MIME: $mimeType")
            }
        }
        
        return extension
    }
    
    /**
     * Get the filename extension from a Content-Disposition header by making a HEAD request
     */
    private suspend fun getExtensionFromContentDisposition(url: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val client = OkHttpClient.Builder()
                    .connectTimeout(5, TimeUnit.SECONDS)
                    .readTimeout(5, TimeUnit.SECONDS)
                    .followRedirects(true)
                    .followSslRedirects(true)
                    .build()
                
                val request = Request.Builder()
                    .url(url)
                    .head() // Use HEAD request to get headers only
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                    .build()
                
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        return@withContext ""
                    }
                    
                    // Check for Content-Disposition header
                    val contentDisposition = response.header("Content-Disposition") ?: return@withContext ""
                    
                    // Try different patterns for Content-Disposition
                    val patterns = listOf(
                        "filename=[\"]?([^\"]*)",  // Standard format
                        "filename\\*=UTF-8''([^;]*)",  // RFC 5987 encoded
                        "filename\\*=([^;]*)",  // RFC 5987 without charset
                        "name=[\"]?([^\"]*)"  // Alternative format
                    )
                    
                    for (pattern in patterns) {
                        val matcher = java.util.regex.Pattern.compile(pattern).matcher(contentDisposition)
                        if (matcher.find()) {
                            val fileName = matcher.group(1)
                            
                            // Decode if URL encoded
                            val decodedFileName = try {
                                java.net.URLDecoder.decode(fileName, "UTF-8")
                            } catch (e: Exception) {
                                fileName
                            }
                            
                            // Get extension from the filename
                            return@withContext getFileExtension(decodedFileName)
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("DownloadService", "Error getting Content-Disposition: ${e.message}")
            }
            return@withContext ""
        }
    }
    
    /**
     * Get file extension from the URL path
     */
    private fun getExtensionFromUrl(url: String): String {
        try {
            // Get the path segment from the URL
            val uri = Uri.parse(url)
            val path = uri.path ?: return ""
            
            // Extract the last segment of the path
            val lastSegment = path.substringAfterLast('/')
            if (lastSegment.isBlank()) return ""
            
            // Remove query parameters and fragment
            val fileNameWithoutParams = lastSegment
                .substringBefore('?')
                .substringBefore('#')
            
            // Return the extension (everything after the last dot)
            return getFileExtension(fileNameWithoutParams)
        } catch (e: Exception) {
            return ""
        }
    }
    
    /**
     * Check if the extension is likely a "fake" one (like .php, .asp, etc.)
     * that indicates a script handling the download rather than the actual file type
     */
    private fun isFakeExtension(extension: String): Boolean {
        val fakeExtensions = setOf(
            "php", "asp", "aspx", "jsp", "cgi", "pl", "py", "rb", "do", "action",
            "html", "htm", "xhtml"
        )
        return fakeExtensions.contains(extension.lowercase())
    }
    
    /**
     * Sanitize a file name by removing invalid characters and limiting length
     */
    private fun sanitizeFileName(fileName: String): String {
        // Replace invalid file system characters with underscores
        var sanitized = fileName.replace(Regex("[\\\\/:*?\"<>|]"), "_")
        
        // Trim whitespace
        sanitized = sanitized.trim()
        
        // Ensure file name isn't empty
        if (sanitized.isBlank()) {
            sanitized = "download"
        }
        
        // Limit file name length (255 is max on most file systems, but we'll be conservative)
        if (sanitized.length > 200) {
            val extension = getFileExtension(sanitized)
            val nameWithoutExt = sanitized.substringBeforeLast('.')
            sanitized = nameWithoutExt.take(196) + (if (extension.isNotBlank()) ".$extension" else "")
        }
        
        return sanitized
    }
    
    /**
     * Get file extension from a file name
     */
    private fun getFileExtension(fileName: String): String {
        val lastDotIndex = fileName.lastIndexOf('.')
        return if (lastDotIndex > 0 && lastDotIndex < fileName.length - 1) {
            fileName.substring(lastDotIndex + 1)
        } else {
            ""
        }
    }
    
    /**
     * Get file extension from MIME type
     */
    private fun getExtensionFromMimeType(mimeType: String): String {
        // Try using MimeTypeMap first
        var extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(mimeType) ?: ""
        
        // If MimeTypeMap fails, use our extended mapping
        if (extension.isBlank()) {
            extension = when (mimeType.lowercase()) {
                "application/zip", "application/x-zip-compressed", "application/zip-compressed" -> "zip"
                "application/pdf" -> "pdf"
                "application/vnd.android.package-archive" -> "apk"
                "application/epub+zip" -> "epub"
                "application/x-mobipocket-ebook" -> "mobi"
                "application/x-ms-application" -> "application"
                "application/msword" -> "doc"
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document" -> "docx"
                "application/vnd.ms-excel" -> "xls"
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" -> "xlsx"
                "application/vnd.ms-powerpoint" -> "ppt"
                "application/vnd.openxmlformats-officedocument.presentationml.presentation" -> "pptx"
                "application/x-rar-compressed", "application/rar" -> "rar"
                "application/x-tar" -> "tar"
                "application/x-7z-compressed" -> "7z"
                "application/x-gzip", "application/gzip" -> "gz"
                "application/x-bzip2" -> "bz2"
                "application/x-debian-package" -> "deb"
                "application/x-redhat-package-manager" -> "rpm"
                "text/plain" -> "txt"
                "text/html" -> "html"
                "text/css" -> "css"
                "text/javascript", "application/javascript" -> "js"
                "application/json" -> "json"
                "application/xml", "text/xml" -> "xml"
                "image/jpeg" -> "jpg"
                "image/png" -> "png"
                "image/gif" -> "gif"
                "image/webp" -> "webp"
                "image/svg+xml" -> "svg"
                "image/bmp" -> "bmp"
                "image/tiff" -> "tiff"
                "image/x-icon" -> "ico"
                "video/mp4" -> "mp4"
                "video/x-matroska" -> "mkv"
                "video/x-msvideo" -> "avi"
                "video/quicktime" -> "mov"
                "video/webm" -> "webm"
                "video/3gpp" -> "3gp"
                "audio/mpeg" -> "mp3"
                "audio/mp4" -> "m4a"
                "audio/ogg" -> "ogg"
                "audio/wav" -> "wav"
                "audio/webm" -> "weba"
                "audio/flac" -> "flac"
                "audio/aac" -> "aac"
                else -> ""
            }
        }
        
        return extension
    }

    /**
     * Detect file type from file signature (magic numbers)
     */
    private fun detectFileTypeFromSignature(inputStream: InputStream): String {
        val buffer = ByteArray(12)
        val bytesRead = inputStream.read(buffer)
        if (bytesRead < 2) return ""

        // Check for ZIP-based formats (ZIP, DOCX, XLSX, PPTX, etc.)
        if (buffer[0] == 0x50.toByte() && buffer[1] == 0x4B.toByte()) {
            // Check for specific ZIP-based formats
            if (bytesRead >= 4) {
                // Check for Office Open XML formats
                if (buffer[2] == 0x03.toByte() && buffer[3] == 0x04.toByte()) {
                    // Check for specific Office formats
                    if (bytesRead >= 30) {
                        val contentTypes = String(buffer, 4, 26)
                        return when {
                            contentTypes.contains("[Content_Types].xml") -> {
                                if (contentTypes.contains("word/")) "docx"
                                else if (contentTypes.contains("xl/")) "xlsx"
                                else if (contentTypes.contains("ppt/")) "pptx"
                                else "zip"
                            }
                            else -> "zip"
                        }
                    }
                }
            }
            return "zip"
        }

        // Check for PDF
        if (buffer[0] == 0x25.toByte() && buffer[1] == 0x50.toByte() && 
            buffer[2] == 0x44.toByte() && buffer[3] == 0x46.toByte()) {
            return "pdf"
        }

        // Check for RAR
        if (buffer[0] == 0x52.toByte() && buffer[1] == 0x61.toByte() && 
            buffer[2] == 0x72.toByte() && buffer[3] == 0x21.toByte()) {
            return "rar"
        }

        // Check for 7Z
        if (buffer[0] == 0x37.toByte() && buffer[1] == 0x7A.toByte() && 
            buffer[2] == 0xBC.toByte() && buffer[3] == 0xAF.toByte()) {
            return "7z"
        }

        // Check for PNG
        if (buffer[0] == 0x89.toByte() && buffer[1] == 0x50.toByte() && 
            buffer[2] == 0x4E.toByte() && buffer[3] == 0x47.toByte()) {
            return "png"
        }

        // Check for JPEG
        if (buffer[0] == 0xFF.toByte() && buffer[1] == 0xD8.toByte()) {
            return "jpg"
        }

        // Check for GIF
        if (buffer[0] == 0x47.toByte() && buffer[1] == 0x49.toByte() && 
            buffer[2] == 0x46.toByte()) {
            return "gif"
        }

        // Check for MP3
        if (buffer[0] == 0x49.toByte() && buffer[1] == 0x44.toByte() && 
            buffer[2] == 0x33.toByte()) {
            return "mp3"
        }

        return ""
    }

}
