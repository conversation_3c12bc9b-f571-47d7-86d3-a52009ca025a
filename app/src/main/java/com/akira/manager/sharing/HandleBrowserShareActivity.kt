package com.akira.manager.sharing

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import com.akira.manager.ui.activity.BrowserActivity

/**
 * Activity that handles shared URLs from other apps and opens them in the MDM browser.
 * This activity appears in the Android share menu when users share web URLs.
 */
class HandleBrowserShareActivity : Activity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (intent?.action == Intent.ACTION_SEND && intent.type == "text/plain") {
            intent.getStringExtra(Intent.EXTRA_TEXT)?.let { sharedText ->
                Log.d("HandleBrowserShare", "Received shared text: $sharedText")
                
                // Extract URL from shared text (could contain additional text)
                val url = extractUrlFromText(sharedText)
                
                if (url != null && isValidWebUrl(url)) {
                    Log.d("HandleBrowserShare", "Opening URL in browser: $url")
                    
                    // Create intent to launch BrowserActivity directly with the URL
                    val browserIntent = Intent(this, BrowserActivity::class.java).apply {
                        putExtra(BrowserActivity.EXTRA_URL, url)
                        // Add flags to ensure proper task management
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                    }
                    
                    startActivity(browserIntent)

                    // Apply smooth Material Design transition animation
                    // Note: Using overridePendingTransition for compatibility with older Android versions
                    @Suppress("DEPRECATION")
                    overridePendingTransition(
                        android.R.anim.slide_in_left,
                        android.R.anim.slide_out_right
                    )
                } else {
                    Log.w("HandleBrowserShare", "Invalid or no URL found in shared text: $sharedText")
                }
            }
        }

        // Finish this intermediate activity immediately
        finish()
    }

    /**
     * Extracts the first valid URL from shared text.
     * Handles cases where the shared text contains additional content besides the URL.
     */
    private fun extractUrlFromText(text: String): String? {
        val trimmedText = text.trim()
        
        // If the entire text is a URL, return it
        if (isValidWebUrl(trimmedText)) {
            return trimmedText
        }
        
        // Look for URLs within the text using regex
        val urlPattern = Regex(
            """https?://[^\s]+""",
            RegexOption.IGNORE_CASE
        )
        
        val matchResult = urlPattern.find(trimmedText)
        return matchResult?.value
    }

    /**
     * Validates if a string is a valid web URL (http or https).
     */
    private fun isValidWebUrl(url: String): Boolean {
        return url.startsWith("http://", ignoreCase = true) || 
               url.startsWith("https://", ignoreCase = true)
    }

    companion object {
        private const val TAG = "HandleBrowserShare"
    }
}
