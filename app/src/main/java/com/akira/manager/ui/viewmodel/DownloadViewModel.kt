package com.akira.manager.ui.viewmodel

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.app.DownloadManager
import android.database.Cursor
import android.os.Build
import android.webkit.MimeTypeMap
import android.webkit.URLUtil
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.akira.manager.data.local.DownloadDatabase
import com.akira.manager.data.model.DownloadItem
import com.akira.manager.data.model.DownloadStatus
import com.akira.manager.data.repository.DownloadRepository
import com.akira.manager.data.service.DownloadService
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import androidx.compose.runtime.mutableStateOf
import java.net.URL

// MainScreenState class to manage UI state for the main screen
class MainScreenState {
    var showAddDownloadDialog = mutableStateOf(false)
    var showFileExtensionDialog = mutableStateOf(false)
    var pendingDownloadInfo = mutableStateOf<Triple<String, String, String>?>(null) // url, fileName, mimeType
    var prefilledUrl = mutableStateOf<String?>(null) // URL to pre-fill in add download dialog
}

class DownloadViewModel(application: Application) : AndroidViewModel(application) {
    val mainScreenState = MainScreenState()
    
    private val repository: DownloadRepository
    private val downloadManager: DownloadManager
    private val downloadReceiver: BroadcastReceiver
    private val progressUpdateJob = SupervisorJob()
    private val progressUpdateScope = CoroutineScope(Dispatchers.IO + progressUpdateJob)
    private val activeProgressTracking = mutableMapOf<Long, Job>()
    private val _downloads = MutableStateFlow<List<DownloadItem>>(emptyList())
    val downloads: StateFlow<List<DownloadItem>> = _downloads.asStateFlow()
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    private var lastSpeedUpdateTime = mutableMapOf<Long, Long>()
    private var lastBytesDownloaded = mutableMapOf<Long, Long>()
    
    // Multi-selection support
    private val _isMultiSelectMode = MutableStateFlow(false)
    val isMultiSelectMode: StateFlow<Boolean> = _isMultiSelectMode.asStateFlow()
    
    private val _selectedDownloads = MutableStateFlow<Set<Long>>(emptySet())
    val selectedDownloads: StateFlow<Set<Long>> = _selectedDownloads.asStateFlow()

    companion object {
        private const val MAX_CONCURRENT_TRACKING = 5
        private const val NOTIFICATION_UPDATE_INTERVAL = 2000L // Update notification every 2 seconds
        private const val PROGRESS_UPDATE_INTERVAL = 500L // Update progress every 500ms
        val Factory: ViewModelProvider.Factory = viewModelFactory {
            initializer {
                val application = (this[ViewModelProvider.AndroidViewModelFactory.APPLICATION_KEY] as Application)
                DownloadViewModel(application)
            }
        }
    }
    
    init {
        val database = DownloadDatabase.getDatabase(application)
        repository = DownloadRepository(database.downloadDao(), application)
        downloadManager = application.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        
        downloadReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == DownloadManager.ACTION_DOWNLOAD_COMPLETE) {
                    val downloadId = intent.getLongExtra(DownloadManager.EXTRA_DOWNLOAD_ID, -1)
                    if (downloadId != -1L) {
                        viewModelScope.launch(Dispatchers.Main) {
                            try {
                                val query = DownloadManager.Query().setFilterById(downloadId)
                                val cursor = downloadManager.query(query)
                                
                                if (cursor.moveToFirst()) {
                                    val status = cursor.getInt(
                                        cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS)
                                    )
                                    val bytesTotal = cursor.getLong(
                                        cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)
                                    )

                                    if (status == DownloadManager.STATUS_SUCCESSFUL) {
                                        // Force update to completed state
                                        repository.updateDownloadProgress(downloadId, bytesTotal, bytesTotal)
                                        repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED)
                                        
                                        // Cancel any existing tracking
                                        synchronized(activeProgressTracking) {
                                            activeProgressTracking[downloadId]?.cancel()
                                            activeProgressTracking.remove(downloadId)
                                        }
                                    } else {
                                        updateDownloadStatus(cursor, downloadId)
                                    }
                                }
                                cursor.close()
                            } catch (e: Exception) {
                                _error.value = "Error updating download status: ${e.message}"
                            }
                        }
                    }
                }
            }
        }
        
        // Register broadcast receiver for download updates with appropriate flags
        val flags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Context.RECEIVER_NOT_EXPORTED
        } else {
            0
        }
        
        application.registerReceiver(
            downloadReceiver,
            IntentFilter(DownloadManager.ACTION_DOWNLOAD_COMPLETE),
            flags
        )
        
        // Observe downloads using viewModelScope to ensure proper lifecycle
        viewModelScope.launch {
            try {
                repository.getAllDownloads().collect { downloads ->
                    _downloads.value = downloads
                    // Filter and track only active downloads
                    val activeDownloads = downloads.filter { 
                        it.status == DownloadStatus.PENDING || 
                        it.status == DownloadStatus.DOWNLOADING 
                    }
                    
                    // Cancel tracking for downloads that are no longer active
                    val activeIds = activeDownloads.map { it.id }.toSet()
                    activeProgressTracking.keys
                        .filterNot { it in activeIds }
                        .forEach { id ->
                            activeProgressTracking[id]?.cancel()
                            activeProgressTracking.remove(id)
                        }
                    
                    // Start tracking for new active downloads
                    activeDownloads.forEach { download ->
                        if (!activeProgressTracking.containsKey(download.id)) {
                            startProgressTracking(download.id)
                        }
                    }
                }
            } catch (e: Exception) {
                _error.value = "Error loading downloads: ${e.message}"
            }
        }
    }

    private fun startProgressTracking(downloadId: Long) {
        synchronized(activeProgressTracking) {
            // Cancel existing job if any
            activeProgressTracking[downloadId]?.cancel()
            
            val job = viewModelScope.launch(Dispatchers.IO) {
                try {
                    lastSpeedUpdateTime[downloadId] = System.currentTimeMillis()
                    lastBytesDownloaded[downloadId] = 0L
                    var shouldContinue = true
                    var lastNotificationUpdate = 0L
                    var noProgressCount = 0
                    var lastBytesValue = 0L

                    while (isActive && shouldContinue) {
                        try {
                            val download = repository.getDownloadById(downloadId)
                            if (download == null) {
                                shouldContinue = false
                            } else {
                                when (download.status) {
                                    DownloadStatus.PAUSED -> {
                                        delay(1000)
                                    }
                                    DownloadStatus.CANCELLED,
                                    DownloadStatus.COMPLETED,
                                    DownloadStatus.FAILED -> {
                                        shouldContinue = false
                                    }
                                    else -> {
                                        val query = DownloadManager.Query().setFilterById(downloadId)
                                        downloadManager.query(query).use { cursor ->
                                            if (cursor.moveToFirst()) {
                                                val status = cursor.getInt(
                                                    cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS)
                                                )
                                                val bytesDownloaded = cursor.getLong(
                                                    cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)
                                                )
                                                val bytesTotal = cursor.getLong(
                                                    cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)
                                                )

                                                // Check if download is actually making progress
                                                if (bytesDownloaded == lastBytesValue) {
                                                    noProgressCount++
                                                } else {
                                                    noProgressCount = 0
                                                    lastBytesValue = bytesDownloaded
                                                }

                                                when (status) {
                                                    DownloadManager.STATUS_SUCCESSFUL -> {
                                                        withContext(Dispatchers.Main) {
                                                            repository.updateDownloadProgress(downloadId, bytesTotal, bytesTotal)
                                                            repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED)
                                                        }
                                                        shouldContinue = false
                                                    }
                                                    DownloadManager.STATUS_FAILED -> {
                                                        val reason = cursor.getInt(
                                                            cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_REASON)
                                                        )
                                                        withContext(Dispatchers.Main) {
                                                            repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                                                            _error.value = getErrorMessage(reason)
                                                        }
                                                        shouldContinue = false
                                                    }
                                                    DownloadManager.STATUS_RUNNING -> {
                                                        val currentTime = System.currentTimeMillis()
                                                        val timeSinceLastUpdate = currentTime - (lastSpeedUpdateTime[downloadId] ?: currentTime)
                                                        val timeSinceLastNotification = currentTime - lastNotificationUpdate

                                                        // Update speed and progress
                                                        if (timeSinceLastUpdate >= PROGRESS_UPDATE_INTERVAL) {
                                                            val bytesDelta = bytesDownloaded - (lastBytesDownloaded[downloadId] ?: 0)
                                                            val speed = bytesDelta / (timeSinceLastUpdate / 1000f)
                                                            
                                                            lastSpeedUpdateTime[downloadId] = currentTime
                                                            lastBytesDownloaded[downloadId] = bytesDownloaded

                                                            // Only update UI if enough time has passed
                                                            if (timeSinceLastNotification >= NOTIFICATION_UPDATE_INTERVAL) {
                                                                withContext(Dispatchers.Main) {
                                                                    // Handle case where downloaded bytes exceed total bytes
                                                                    // This can happen when server returns incorrect Content-Length
                                                                    if (bytesTotal > 0 && bytesDownloaded > bytesTotal) {
                                                                        // Update the totalBytes to match the actual downloaded bytes
                                                                        // This will prevent progress over 100%
                                                                        repository.updateDownloadTotalSize(downloadId, bytesDownloaded)
                                                                        repository.updateDownloadProgress(downloadId, bytesDownloaded, bytesDownloaded)
                                                                    } else {
                                                                        repository.updateDownloadProgress(downloadId, bytesDownloaded, bytesTotal)
                                                                    }
                                                                }
                                                                lastNotificationUpdate = currentTime
                                                            }
                                                        }

                                                        // Check for completion conditions
                                                        if (bytesTotal > 0 && (
                                                            bytesDownloaded >= bytesTotal || // Exact completion
                                                            (bytesDownloaded > 0 && noProgressCount > 10 && 
                                                             bytesDownloaded >= bytesTotal * 0.99) // Stuck at 99%+ for a while
                                                        )) {
                                                            withContext(Dispatchers.Main) {
                                                                repository.updateDownloadProgress(downloadId, bytesTotal, bytesTotal)
                                                                repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED)
                                                            }
                                                            shouldContinue = false
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            if (shouldContinue) {
                                delay(PROGRESS_UPDATE_INTERVAL)
                            }
                        } catch (e: CancellationException) {
                            throw e
                        } catch (e: Exception) {
                            // Log error but continue tracking
                            e.printStackTrace()
                            delay(1000)
                        }
                    }
                } finally {
                    withContext(Dispatchers.Main) {
                        synchronized(activeProgressTracking) {
                            activeProgressTracking.remove(downloadId)
                        }
                        lastSpeedUpdateTime.remove(downloadId)
                        lastBytesDownloaded.remove(downloadId)
                    }
                }
            }

            activeProgressTracking[downloadId] = job
        }
    }

    private suspend fun updateDownloadStatus(cursor: Cursor, downloadId: Long) {
        try {
            val statusIndex = cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_STATUS)
            val reasonIndex = cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_REASON)
            val bytesDownloadedIndex = cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_BYTES_DOWNLOADED_SO_FAR)
            val bytesTotalIndex = cursor.getColumnIndexOrThrow(DownloadManager.COLUMN_TOTAL_SIZE_BYTES)
            
            val status = cursor.getInt(statusIndex)
            val reason = cursor.getInt(reasonIndex)
            val bytesDownloaded = cursor.getLong(bytesDownloadedIndex)
            val bytesTotal = cursor.getLong(bytesTotalIndex)
            
            when (status) {
                DownloadManager.STATUS_SUCCESSFUL -> {
                    repository.updateDownloadProgress(downloadId, bytesDownloaded, bytesTotal)
                    repository.updateDownloadStatus(downloadId, DownloadStatus.COMPLETED, 100)
                }
                DownloadManager.STATUS_FAILED -> {
                    val errorMessage = getErrorMessage(reason)
                    repository.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                    _error.value = errorMessage
                }
                DownloadManager.STATUS_RUNNING -> {
                    repository.updateDownloadProgress(downloadId, bytesDownloaded, bytesTotal)
                }
            }
        } catch (e: Exception) {
            _error.value = "Error updating download status: ${e.message}"
        }
    }

    private fun getErrorMessage(reason: Int): String {
        return when (reason) {
            DownloadManager.ERROR_CANNOT_RESUME -> "Cannot resume download"
            DownloadManager.ERROR_DEVICE_NOT_FOUND -> "Device not found"
            DownloadManager.ERROR_FILE_ALREADY_EXISTS -> "File already exists"
            DownloadManager.ERROR_FILE_ERROR -> "File error"
            DownloadManager.ERROR_HTTP_DATA_ERROR -> "Network data error"
            DownloadManager.ERROR_INSUFFICIENT_SPACE -> "Insufficient space"
            DownloadManager.ERROR_TOO_MANY_REDIRECTS -> "Too many redirects"
            DownloadManager.ERROR_UNHANDLED_HTTP_CODE -> "Unhandled HTTP code"
            DownloadManager.ERROR_UNKNOWN -> "Unknown error"
            else -> "Download failed"
        }
    }

    private fun getMimeTypeFromUrl(url: String): String {
        try {
            // First try to get MIME type from URL extension
            val extension = MimeTypeMap.getFileExtensionFromUrl(url)
            val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
            if (!mimeType.isNullOrBlank()) {
                return mimeType
            }

            // If that fails, try to get it from the URL path
            val path = URL(url).path
            val pathExtension = path.substringAfterLast('.', "").lowercase()
            if (pathExtension.isNotBlank()) {
                val pathMimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(pathExtension)
                if (!pathMimeType.isNullOrBlank()) {
                    return pathMimeType
                }
            }

            // Check for common file types in URL
            return when {
                url.endsWith(".zip", ignoreCase = true) -> "application/zip"
                url.endsWith(".pdf", ignoreCase = true) -> "application/pdf"
                url.endsWith(".apk", ignoreCase = true) -> "application/vnd.android.package-archive"
                url.endsWith(".docx", ignoreCase = true) -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                url.endsWith(".xlsx", ignoreCase = true) -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                url.endsWith(".pptx", ignoreCase = true) -> "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                url.endsWith(".rar", ignoreCase = true) -> "application/x-rar-compressed"
                url.endsWith(".7z", ignoreCase = true) -> "application/x-7z-compressed"
                url.endsWith(".tar", ignoreCase = true) -> "application/x-tar"
                url.endsWith(".gz", ignoreCase = true) -> "application/x-gzip"
                url.endsWith(".mp3", ignoreCase = true) -> "audio/mpeg"
                url.endsWith(".mp4", ignoreCase = true) -> "video/mp4"
                url.endsWith(".jpg", ignoreCase = true) || url.endsWith(".jpeg", ignoreCase = true) -> "image/jpeg"
                url.endsWith(".png", ignoreCase = true) -> "image/png"
                url.endsWith(".gif", ignoreCase = true) -> "image/gif"
                url.endsWith(".webp", ignoreCase = true) -> "image/webp"
                url.endsWith(".txt", ignoreCase = true) -> "text/plain"
                url.endsWith(".html", ignoreCase = true) || url.endsWith(".htm", ignoreCase = true) -> "text/html"
                url.endsWith(".css", ignoreCase = true) -> "text/css"
                url.endsWith(".js", ignoreCase = true) -> "application/javascript"
                url.endsWith(".json", ignoreCase = true) -> "application/json"
                url.endsWith(".xml", ignoreCase = true) -> "application/xml"
                else -> "application/octet-stream"
            }
        } catch (e: Exception) {
            return "application/octet-stream"
        }
    }

    private fun getFileNameFromUrl(url: String): String {
        try {
            // First try URLUtil
            var fileName = URLUtil.guessFileName(url, null, null)
            
            // If that doesn't work, try to get it from the URL path
            if (fileName.isNullOrBlank() || fileName == "download") {
                val decodedUrl = URLDecoder.decode(url, StandardCharsets.UTF_8.name())
                val path = URL(decodedUrl).path
                fileName = path.substringAfterLast('/')
                    .substringBefore('?')
                    .substringBefore('#')
            }
            
            // If we still don't have a valid filename, generate one
            if (fileName.isBlank()) {
                fileName = "download_${System.currentTimeMillis()}"
            }

            // If it's a webpage, ensure it has .html extension
            if (isWebpageUrl(url)) {
                if (!fileName.endsWith(".html", ignoreCase = true)) {
                    fileName = "$fileName.html"
                }
            }
            
            return fileName
        } catch (e: Exception) {
            return "download_${System.currentTimeMillis()}"
        }
    }

    private fun isWebpageUrl(url: String): Boolean {
        return try {
            // Check for common download file extensions first
            val isDownloadFile = url.endsWith(".zip", ignoreCase = true) ||
                               url.endsWith(".pdf", ignoreCase = true) ||
                               url.endsWith(".apk", ignoreCase = true) ||
                               url.endsWith(".exe", ignoreCase = true) ||
                               url.endsWith(".dmg", ignoreCase = true) ||
                               url.endsWith(".iso", ignoreCase = true) ||
                               url.endsWith(".rar", ignoreCase = true) ||
                               url.endsWith(".7z", ignoreCase = true) ||
                               url.endsWith(".tar", ignoreCase = true) ||
                               url.endsWith(".gz", ignoreCase = true) ||
                               url.endsWith(".mp3", ignoreCase = true) ||
                               url.endsWith(".mp4", ignoreCase = true) ||
                               url.endsWith(".avi", ignoreCase = true) ||
                               url.endsWith(".mkv", ignoreCase = true) ||
                               url.endsWith(".jpg", ignoreCase = true) ||
                               url.endsWith(".jpeg", ignoreCase = true) ||
                               url.endsWith(".png", ignoreCase = true) ||
                               url.endsWith(".gif", ignoreCase = true) ||
                               url.endsWith(".webp", ignoreCase = true) ||
                               url.endsWith(".doc", ignoreCase = true) ||
                               url.endsWith(".docx", ignoreCase = true) ||
                               url.endsWith(".xls", ignoreCase = true) ||
                               url.endsWith(".xlsx", ignoreCase = true) ||
                               url.endsWith(".txt", ignoreCase = true) ||
                               url.endsWith(".csv", ignoreCase = true)

            // If it's a download file, it's not a webpage
            if (isDownloadFile) return false

            // Check for common webpage indicators
            val isWebpage = url.endsWith(".html", ignoreCase = true) ||
                           url.endsWith(".htm", ignoreCase = true) ||
                           url.endsWith(".php", ignoreCase = true) ||
                           url.endsWith(".asp", ignoreCase = true) ||
                           url.endsWith(".aspx", ignoreCase = true) ||
                           url.endsWith(".jsp", ignoreCase = true) ||
                           url.contains("/index.", ignoreCase = true) ||
                           url.contains("/page.", ignoreCase = true) ||
                           url.contains("/article.", ignoreCase = true) ||
                           url.contains("/post.", ignoreCase = true) ||
                           url.contains("/blog.", ignoreCase = true) ||
                           url.contains("/news.", ignoreCase = true) ||
                           url.contains("/download.php", ignoreCase = true) ||
                           url.contains("/download.asp", ignoreCase = true) ||
                           url.contains("/download.aspx", ignoreCase = true) ||
                           url.contains("/download.jsp", ignoreCase = true) ||
                           url.matches(Regex("^https?://[^/]+/?$")) // Matches root URLs like https://www.google.com/

            // If it's clearly a webpage, return true
            if (isWebpage) return true

            // Check MIME type as a last resort
            val mimeType = getMimeTypeFromUrl(url)
            mimeType.startsWith("text/html") || 
            mimeType.startsWith("application/xhtml")

        } catch (e: Exception) {
            false
        }
    }

    fun addDownload(url: String, fileName: String? = null, mimeType: String? = null) {
        viewModelScope.launch {
            _isLoading.value = true
            var dialogShown = false
            try {
                _error.value = null // Clear previous errors

                val processedUrl = URLDecoder.decode(url, StandardCharsets.UTF_8.name())

                // Step 1: Determine the initial base filename.
                // Use provided `fileName` if available. Otherwise, guess from URL.
                // Pass the original `mimeType` (from parameter) as a hint to guessFileName.
                // If `mimeType` is null, guessFileName will rely on URL extension or its own logic.
                var initialBaseFileName = fileName
                if (initialBaseFileName.isNullOrBlank()) {
                    initialBaseFileName = URLUtil.guessFileName(
                        processedUrl,
                        null, // contentDisposition - assuming not available here for the general case
                        mimeType // Pass the original mimeType parameter as a hint
                    )
                }
                if (initialBaseFileName.isNullOrBlank()) {
                    initialBaseFileName = "download" // Fallback if still blank
                }

                // Step 2: Sanitize the determined filename (remove query params/fragments).
                var sanitizedFileName = initialBaseFileName
                val questionMarkIndex = sanitizedFileName.lastIndexOf('?')
                if (questionMarkIndex != -1) {
                    sanitizedFileName = sanitizedFileName.substring(0, questionMarkIndex)
                }
                val hashIndex = sanitizedFileName.lastIndexOf('#')
                if (hashIndex != -1) {
                    sanitizedFileName = sanitizedFileName.substring(0, hashIndex)
                }

                // Step 3: Get the extension from the sanitized filename for the .bin check.
                val currentFileExtension = sanitizedFileName.substringAfterLast('.', "").lowercase()

                // Step 4: Decide whether to show the dialog or download directly.
                if (currentFileExtension == "bin") {
                    // For .bin files, prepare and show the dialog.
                    // Determine MIME type for the dialog: use provided, else derive from "bin", else fallback.
                    val mimeTypeForDialog = mimeType ?: MimeTypeMap.getSingleton()
                        .getMimeTypeFromExtension("bin") // Use "bin" as we are in the .bin branch
                        ?: "application/octet-stream"

                    mainScreenState.pendingDownloadInfo.value = Triple(processedUrl, sanitizedFileName, mimeTypeForDialog)
                    mainScreenState.showFileExtensionDialog.value = true
                    dialogShown = true
                    // _isLoading remains true; will be handled by dialog actions.
                } else {
                    // For non-.bin files, proceed with download directly.
                    // Determine final MIME type: use provided, else derive from actual extension, else fallback.
                    val finalMimeType = mimeType ?: MimeTypeMap.getSingleton()
                        .getMimeTypeFromExtension(currentFileExtension.ifEmpty { null })
                        ?: "application/octet-stream"

                    repository.addDownload(processedUrl, sanitizedFileName, finalMimeType)
                    // _isLoading will be set to false in the finally block because dialogShown is false.
                }
            } catch (e: Exception) {
                _error.value = "Failed to prepare download: ${e.message}"
                // If an exception occurs, _isLoading will be handled by the finally block.
            } finally {
                if (!dialogShown) {
                    _isLoading.value = false
                }
            }
        }
    }

    fun startDownloadWithEditedFileName(editedFileName: String) {
        val pendingInfo = mainScreenState.pendingDownloadInfo.value
        // Clear pending state and hide dialog immediately
        mainScreenState.pendingDownloadInfo.value = null
        mainScreenState.showFileExtensionDialog.value = false

        if (pendingInfo != null) {
            val (url, _, _) = pendingInfo // We'll use original URL, original mimeType is not used, new one derived
            
            viewModelScope.launch {
                // _isLoading should already be true if the dialog was shown from addDownload
                try {
                    _error.value = null // Clear previous errors
                    val newMimeType = MimeTypeMap.getSingleton()
                        .getMimeTypeFromExtension(editedFileName.substringAfterLast('.', ""))
                        ?: "application/octet-stream"
                    repository.addDownload(url, editedFileName, newMimeType)
                } catch (e: Exception) {
                    _error.value = "Failed to start download with edited name: ${e.message}"
                } finally {
                    _isLoading.value = false // Ensure isLoading is reset
                }
            }
        } else {
            // Should not happen if dialog was shown, but as a safeguard:
            _isLoading.value = false
        }
    }

    fun dismissFileExtensionDialog() {
        mainScreenState.showFileExtensionDialog.value = false
        mainScreenState.pendingDownloadInfo.value = null
        _isLoading.value = false // Reset isLoading state
    }

    fun addExternalDownload(url: String, fileName: String, mimeType: String, source: String) {
        viewModelScope.launch {
            try {
                val downloadId = repository.addDownload(url, fileName, mimeType, source)
                startProgressTracking(downloadId)
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to add download"
            }
        }
    }

    fun setPrefilledUrl(url: String) {
        mainScreenState.prefilledUrl.value = url
    }

    fun clearPrefilledUrl() {
        mainScreenState.prefilledUrl.value = null
    }

    fun deleteDownload(download: DownloadItem) {
        viewModelScope.launch {
            try {
                // Cancel notification if applicable
                if (download.status == DownloadStatus.PAUSED || download.status == DownloadStatus.FAILED) {
                    val intent = Intent(getApplication(), DownloadService::class.java).apply {
                        action = DownloadService.ACTION_CANCEL_NOTIFICATION
                        putExtra(DownloadService.EXTRA_DOWNLOAD_ID, download.id)
                    }
                    getApplication<Application>().startService(intent)
                }
                
                // If it was being actively tracked, stop that
                download.id?.let { id ->
                    activeProgressTracking[id]?.cancel()
                    activeProgressTracking.remove(id)
                }
                
                // Remove from our database
                repository.deleteDownload(download)
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to delete download"
            }
        }
    }
    
    fun deleteMultipleDownloads(downloads: List<DownloadItem>, deleteFromDevice: Boolean = false) {
        viewModelScope.launch {
            try {
                downloads.forEach { download ->
                    // Cancel notification if applicable
                    if (download.status == DownloadStatus.PAUSED || download.status == DownloadStatus.FAILED) {
                        val intent = Intent(getApplication(), DownloadService::class.java).apply {
                            action = DownloadService.ACTION_CANCEL_NOTIFICATION
                            putExtra(DownloadService.EXTRA_DOWNLOAD_ID, download.id)
                        }
                        getApplication<Application>().startService(intent)
                    }
                    
                    // If being tracked, stop tracking
                    download.id?.let { id ->
                        activeProgressTracking[id]?.cancel()
                        activeProgressTracking.remove(id)
                    }
                }
                
                // Remove all from database at once with file deletion if requested
                repository.deleteDownloads(downloads, deleteFromDevice)
                
                // Clear selection after deletion
                clearSelectedDownloads()
                setMultiSelectMode(false)
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to delete selected downloads"
            }
        }
    }
    
    // Multi-selection management methods
    fun toggleMultiSelectMode(enable: Boolean? = null) {
        _isMultiSelectMode.value = enable ?: !_isMultiSelectMode.value
        if (!_isMultiSelectMode.value) {
            clearSelectedDownloads()
        }
    }
    
    fun setMultiSelectMode(enable: Boolean) {
        _isMultiSelectMode.value = enable
        if (!enable) {
            clearSelectedDownloads()
        }
    }
    
    fun toggleDownloadSelection(downloadId: Long) {
        val currentSelections = _selectedDownloads.value.toMutableSet()
        if (currentSelections.contains(downloadId)) {
            currentSelections.remove(downloadId)
        } else {
            currentSelections.add(downloadId)
        }
        _selectedDownloads.value = currentSelections
        
        // If all items are deselected, exit multi-select mode
        if (currentSelections.isEmpty()) {
            setMultiSelectMode(false)
        }
    }
    
    fun isDownloadSelected(downloadId: Long): Boolean {
        return _selectedDownloads.value.contains(downloadId)
    }
    
    fun getSelectedDownloads(): List<DownloadItem> {
        return _downloads.value.filter { download ->
            download.id?.let { _selectedDownloads.value.contains(it) } ?: false
        }
    }
    
    fun clearSelectedDownloads() {
        _selectedDownloads.value = emptySet()
    }
    
    fun selectAllCompletedDownloads() {
        val completedDownloadIds = _downloads.value
            .filter { it.status == DownloadStatus.COMPLETED }
            .mapNotNull { it.id }
            .toSet()
        
        if (completedDownloadIds.isNotEmpty()) {
            _selectedDownloads.value = completedDownloadIds
            setMultiSelectMode(true)
        }
    }

    fun clearCompletedDownloads() {
        viewModelScope.launch {
            try {
                repository.clearCompletedDownloads()
            } catch (e: Exception) {
                _error.value = e.message ?: "Failed to clear completed downloads"
            }
        }
    }

    fun pauseDownload(downloadId: Long) {
        viewModelScope.launch(Dispatchers.Main) {
            try {
                // Update the status in the database
                repository.updateDownloadStatus(downloadId, DownloadStatus.PAUSED)
                
                // Send pause action to the service
                val intent = Intent(getApplication(), DownloadService::class.java).apply {
                    action = DownloadService.ACTION_PAUSE_DOWNLOAD
                    putExtra(DownloadService.EXTRA_DOWNLOAD_ID, downloadId)
                }
                getApplication<Application>().startService(intent)
            } catch (e: Exception) {
                _error.value = "Failed to pause download: ${e.message}"
            }
        }
    }

    fun resumeDownload(downloadId: Long) {
        viewModelScope.launch(Dispatchers.Main) {
            try {
                val download = repository.getDownloadById(downloadId)
                if (download != null) {
                    // Update the status in the database
                    repository.updateDownloadStatus(downloadId, DownloadStatus.DOWNLOADING)
                    
                    // Send resume action to the service
                    val intent = Intent(getApplication(), DownloadService::class.java).apply {
                        action = DownloadService.ACTION_RESUME_DOWNLOAD
                        putExtra(DownloadService.EXTRA_DOWNLOAD_ID, downloadId)
                        putExtra(DownloadService.EXTRA_URL, download.url)
                        putExtra(DownloadService.EXTRA_FILE_NAME, download.fileName)
                        putExtra(DownloadService.EXTRA_MIME_TYPE, download.mimeType)
                    }
                    getApplication<Application>().startService(intent)
                    
                    // Also force a refresh of this download
                    startProgressTracking(downloadId)
                }
            } catch (e: Exception) {
                _error.value = "Failed to resume download: ${e.message}"
            }
        }
    }

    fun retryDownload(downloadId: Long) {
        viewModelScope.launch(Dispatchers.Main) {
            try {
                val download = repository.getDownloadById(downloadId)
                if (download != null) {
                    // Update the status in the database
                    repository.updateDownloadStatus(downloadId, DownloadStatus.PENDING)
                    
                    // Send retry action to the service
                    val intent = Intent(getApplication(), DownloadService::class.java).apply {
                        putExtra(DownloadService.EXTRA_DOWNLOAD_ID, downloadId)
                        putExtra(DownloadService.EXTRA_URL, download.url)
                        putExtra(DownloadService.EXTRA_FILE_NAME, download.fileName)
                        putExtra(DownloadService.EXTRA_MIME_TYPE, download.mimeType)
                    }
                    getApplication<Application>().startService(intent)
                    
                    // Also force a refresh of this download
                    startProgressTracking(downloadId)
                }
            } catch (e: Exception) {
                _error.value = "Failed to retry download: ${e.message}"
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        try {
            synchronized(activeProgressTracking) {
                activeProgressTracking.values.forEach { it.cancel() }
                activeProgressTracking.clear()
            }
            progressUpdateJob.cancel()
            getApplication<Application>().unregisterReceiver(downloadReceiver)
        } catch (e: Exception) {
            // Ignore if receiver is already unregistered
        }
    }
}