package com.akira.manager.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Language // Replace Web with Language
import androidx.compose.material.icons.filled.ContentPaste
import androidx.compose.material.icons.filled.Download
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.akira.manager.ui.viewmodel.DownloadViewModel
import kotlinx.coroutines.launch
import com.akira.manager.ui.theme.PurpleColor
import com.akira.manager.ui.theme.DarkPurpleColor
import com.akira.manager.ui.theme.BlackColor
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.draw.blur
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.unit.sp
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import com.akira.manager.data.local.SettingsManager
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.animation.core.tween
import androidx.compose.runtime.LaunchedEffect
import android.content.ClipDescription
import android.webkit.URLUtil
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.luminance
import com.akira.manager.ui.theme.DarkGrayColor
import com.akira.manager.ui.theme.AmoledSurfaceVariant
import androidx.compose.runtime.saveable.rememberSaveable
import com.akira.manager.ui.components.FileExtensionEditDialog

/**
 * Gets the appropriate background color for dialog components.
 * In Material You dark theme, it uses a toned down secondary container color for better contrast.
 * In AMOLED theme, it uses surfaceVariant which is already configured properly.
 */
@Composable
fun getDialogBackgroundColor(): Color {
    val isAmoledDarkMode by SettingsManager(LocalContext.current).isAmoledDarkMode.collectAsState(initial = false)
    val darkTheme = MaterialTheme.colorScheme.background.luminance() < 0.5
    
    return if (isAmoledDarkMode && darkTheme) {
        // In AMOLED mode, use the standard surfaceVariant which is already dark
        MaterialTheme.colorScheme.surfaceVariant
    } else if (darkTheme) {
        // In Material You dark theme, use secondary container with reduced brightness
        val secondaryColor = MaterialTheme.colorScheme.secondaryContainer
        // Make it darker for better contrast
        Color(
            red = (secondaryColor.red * 0.6f).coerceIn(0f, 1f),
            green = (secondaryColor.green * 0.6f).coerceIn(0f, 1f),
            blue = (secondaryColor.blue * 0.6f).coerceIn(0f, 1f),
            alpha = 1f
        )
    } else {
        // In light theme, use standard surface variant
        MaterialTheme.colorScheme.surfaceVariant
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun MainScreen(
    viewModel: DownloadViewModel,
    onSettingsClick: () -> Unit,
    onBrowserClick: () -> Unit = {}
) {
    // Use the dialog state from the ViewModel
    val showDownloadDialog = viewModel.mainScreenState.showAddDownloadDialog
    val pagerState = rememberPagerState(pageCount = { 2 })
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val settingsManager = remember { SettingsManager(context) }
    
    // Use rememberSaveable to persist state across recompositions
    val clipboardUrl = rememberSaveable { mutableStateOf<String?>(null) }
    val isClipboardDetectionEnabled by settingsManager.isClipboardDetectionEnabled.collectAsState(initial = true)
    // Use rememberSaveable to maintain clipboard check state even when navigating back from settings
    val hasCheckedClipboard = rememberSaveable { mutableStateOf(false) }
    
    // Check clipboard for URLs when app starts (only if enabled)
    LaunchedEffect(Unit) {
        if (isClipboardDetectionEnabled && !hasCheckedClipboard.value) {
            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            if (clipboardManager.hasPrimaryClip() && clipboardManager.primaryClip?.description?.hasMimeType(ClipDescription.MIMETYPE_TEXT_PLAIN) == true) {
                val clipText = clipboardManager.primaryClip?.getItemAt(0)?.text.toString()
                // Check if text is a valid URL
                if (clipText.isNotBlank() && (clipText.startsWith("http://") || clipText.startsWith("https://") || URLUtil.isValidUrl(clipText))) {
                    clipboardUrl.value = clipText
                }
            }
            // Mark that we've checked the clipboard
            hasCheckedClipboard.value = true
        }
    }
    
    if (showDownloadDialog.value) {
        AddDownloadDialog(
            onDismiss = { viewModel.mainScreenState.showAddDownloadDialog.value = false },
            onDownloadAdded = { url ->
                viewModel.addDownload(url)
                viewModel.mainScreenState.showAddDownloadDialog.value = false
            },
            viewModel = viewModel
        )
    }
    
    // Show clipboard URL detection dialog
    clipboardUrl.value?.let { url ->
        ClipboardUrlDialog(
            url = url,
            onDismiss = {
                clipboardUrl.value = null
            },
            onDownload = {
                viewModel.addDownload(url)
                clipboardUrl.value = null
            }
        )
    }

    // Show file extension edit dialog
    if (viewModel.mainScreenState.showFileExtensionDialog.value) {
        val pendingInfo = viewModel.mainScreenState.pendingDownloadInfo.value
        if (pendingInfo != null) {
            val (_, fileName, _) = pendingInfo
            FileExtensionEditDialog(
                initialFileName = fileName,
                onDismiss = {
                    viewModel.dismissFileExtensionDialog()
                },
                onConfirm = { editedFileName: String ->
                    viewModel.startDownloadWithEditedFileName(editedFileName)
                }
            )
        }
    }

    Scaffold(
        modifier = Modifier.fillMaxSize(),
        containerColor = MaterialTheme.colorScheme.background,
        contentWindowInsets = WindowInsets(0, 0, 0, 0),
        topBar = {
            CenterAlignedTopAppBar(
                modifier = Modifier.statusBarsPadding(),
                title = { 
                    Text(
                        "MDM",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold
                        ),
                        color = MaterialTheme.colorScheme.onSurface
                    )
                },
                actions = {
                    IconButton(onClick = onBrowserClick) {
                        Icon(
                            Icons.Default.Language,
                            contentDescription = "Browser",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                    IconButton(onClick = onSettingsClick) {
                        Icon(
                            Icons.Default.Settings,
                            contentDescription = "Settings",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface,
                    actionIconContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                modifier = Modifier.navigationBarsPadding(),
                onClick = { viewModel.mainScreenState.showAddDownloadDialog.value = true },
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onPrimary,
            ) {
                Icon(Icons.Default.Add, contentDescription = "Add Download")
            }
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            TabRow(
                selectedTabIndex = pagerState.currentPage,
                containerColor = MaterialTheme.colorScheme.surface,
                contentColor = MaterialTheme.colorScheme.onSurface,
                divider = {},
                indicator = { tabPositions ->
                    Box(
                        Modifier
                            .tabIndicatorOffset(tabPositions[pagerState.currentPage])
                            .fillMaxWidth(0.5f)
                            .height(3.dp)
                            .padding(horizontal = 50.dp)
                            .background(
                                color = MaterialTheme.colorScheme.primary,
                                shape = MaterialTheme.shapes.small
                            )
                    )
                }
            ) {
                Tab(
                    selected = pagerState.currentPage == 0,
                    onClick = { coroutineScope.launch { pagerState.animateScrollToPage(0, animationSpec = tween(100)) } },
                    text = { 
                        Text(
                            "Downloading",
                            style = MaterialTheme.typography.titleMedium,
                            color = if (pagerState.currentPage == 0) 
                                MaterialTheme.colorScheme.primary 
                            else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                )
                Tab(
                    selected = pagerState.currentPage == 1,
                    onClick = { coroutineScope.launch { pagerState.animateScrollToPage(1, animationSpec = tween(100)) } },
                    text = { 
                        Text(
                            "Completed",
                            style = MaterialTheme.typography.titleMedium,
                            color = if (pagerState.currentPage == 1) 
                                MaterialTheme.colorScheme.primary 
                            else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                )
            }

            HorizontalPager(
                state = pagerState,
                modifier = Modifier.weight(1f)
            ) { page ->
                when (page) {
                    0 -> DownloadQueueScreen(viewModel)
                    1 -> DownloadedScreen(viewModel)
                }
            }
        }
    }
}

@Composable
private fun ClipboardUrlDialog(
    url: String,
    onDismiss: () -> Unit,
    onDownload: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = getDialogBackgroundColor(),
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        textContentColor = MaterialTheme.colorScheme.onSurfaceVariant,
        shape = RoundedCornerShape(28.dp),
        icon = { Icon(Icons.Default.Download, contentDescription = null) },
        title = { Text("URL Detected", style = MaterialTheme.typography.headlineSmall) },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text("Would you like to download from the following URL?")
                Text(
                    text = url,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onDownload,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.15f),
                    contentColor = MaterialTheme.colorScheme.primary,
                    disabledContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                    disabledContentColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                ),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text("Download")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text("Ignore")
            }
        }
    )
}

@Composable
private fun AddDownloadDialog(
    onDismiss: () -> Unit,
    onDownloadAdded: (String) -> Unit,
    viewModel: DownloadViewModel
) {
    val prefilledUrl = viewModel.mainScreenState.prefilledUrl.value
    var url by remember { mutableStateOf("") }

    // Update URL when prefilledUrl changes
    LaunchedEffect(prefilledUrl) {
        if (!prefilledUrl.isNullOrEmpty()) {
            url = prefilledUrl
        }
    }
    
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }
    val context = LocalContext.current
    
    // Request focus when dialog opens
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    // Clear pre-filled URL when dialog is dismissed
    DisposableEffect(Unit) {
        onDispose {
            viewModel.clearPrefilledUrl()
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = getDialogBackgroundColor(),
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        textContentColor = MaterialTheme.colorScheme.onSurfaceVariant,
        shape = RoundedCornerShape(28.dp),
        title = { Text("Add Download", style = MaterialTheme.typography.headlineSmall) },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = url,
                    onValueChange = { url = it },
                    label = { Text("URL") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .focusRequester(focusRequester),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                        focusedTextColor = MaterialTheme.colorScheme.onSurface,
                        unfocusedTextColor = MaterialTheme.colorScheme.onSurface
                    ),
                    shape = RoundedCornerShape(16.dp),
                    singleLine = true,
                    interactionSource = remember { MutableInteractionSource() },
                    keyboardOptions = KeyboardOptions(
                        autoCorrect = false,
                        keyboardType = KeyboardType.Text,
                        imeAction = ImeAction.Done
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (url.isNotBlank()) {
                                onDownloadAdded(url)
                            } else {
                                keyboardController?.hide()
                                focusManager.clearFocus()
                            }
                        }
                    ),
                    trailingIcon = {
                        IconButton(onClick = {
                            val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            if (clipboardManager.hasPrimaryClip() && clipboardManager.primaryClip?.description?.hasMimeType(ClipDescription.MIMETYPE_TEXT_PLAIN) == true) {
                                val clipText = clipboardManager.primaryClip?.getItemAt(0)?.text.toString()
                                if (clipText.isNotBlank()) {
                                    url = clipText
                                }
                            }
                        }) {
                            Icon(
                                imageVector = Icons.Default.ContentPaste,
                                contentDescription = "Paste from clipboard",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    }
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    if (url.isNotBlank()) {
                        // Ensure URL has http/https prefix
                        val processedUrl = if (!url.startsWith("http://") && !url.startsWith("https://")) {
                            "https://$url"
                        } else {
                            url
                        }
                        onDownloadAdded(processedUrl)
                    }
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.15f),
                    contentColor = MaterialTheme.colorScheme.primary,
                    disabledContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                    disabledContentColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                ),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text("Cancel")
            }
        }
    )
}
