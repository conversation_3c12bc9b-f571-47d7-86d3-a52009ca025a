package com.akira.manager.ui.screen

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Folder
import androidx.compose.material.icons.filled.PowerSettingsNew
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.ContentPaste
import androidx.compose.material.icons.filled.SecurityUpdate

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.akira.manager.data.local.SettingsManager

import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import androidx.documentfile.provider.DocumentFile

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onBackClick: () -> Unit,
    settingsManager: SettingsManager
) {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    var showPathDialog by remember { mutableStateOf(false) }
    var customPath by remember { mutableStateOf(settingsManager.getCustomDownloadPath()) }
    
    // Get a friendly display name for the download path
    val displayPath = remember(customPath) {
        when {
            customPath.isEmpty() -> "Default (Downloads/MDM)"
            customPath.contains("/storage/emulated/0/") -> 
                "Internal storage/${customPath.substringAfter("/storage/emulated/0/")}"
            customPath.contains("content://") -> {
                val uri = Uri.parse(customPath)
                val docFile = DocumentFile.fromTreeUri(context, uri)
                docFile?.name ?: "Selected folder"
            }
            customPath.contains("/Download/") -> "Downloads/${customPath.substringAfter("/Download/")}"
            else -> customPath
        }
    }
    
    // Directory picker for selecting custom download folder
    val directoryPicker = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let {
            try {
                // Persist permission
                val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                context.contentResolver.takePersistableUriPermission(uri, takeFlags)
                
                // Store the URI string directly
                val uriString = uri.toString()
                customPath = uriString
                settingsManager.setCustomDownloadPath(uriString)
                
                // Show confirmation toast
                android.widget.Toast.makeText(
                    context,
                    "Download location updated",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            } catch (e: Exception) {
                // Handle error
                android.widget.Toast.makeText(
                    context,
                    "Error setting download location: ${e.message}",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }
    }



    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Settings") },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Frequently Used Settings Section
                Text(
                    text = "Main Settings",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(bottom = 4.dp)
                )

                // Download Path Setting
                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(MaterialTheme.shapes.medium)
                        .clickable { showPathDialog = true }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.Folder,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "Download Location",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = displayPath,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                            }
                        }
                        Icon(
                            Icons.Default.ChevronRight,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // AMOLED Dark Theme Setting
                val isAmoledDarkMode by settingsManager.isAmoledDarkMode.collectAsState(initial = false)
                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(MaterialTheme.shapes.medium)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.DarkMode,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "AMOLED Dark Theme",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = "True black dark mode for AMOLED displays",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        Switch(
                            checked = isAmoledDarkMode,
                            onCheckedChange = { enabled ->
                                scope.launch {
                                    android.util.Log.d("ThemeDebug", "Setting AMOLED mode to: $enabled")
                                    settingsManager.setAmoledDarkMode(enabled)
                                    // MainActivity now handles recreation
                                }
                            },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = MaterialTheme.colorScheme.primary,
                                checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                                uncheckedThumbColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                uncheckedTrackColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                            )
                        )
                    }
                }

                // Clipboard URL Detection Setting
                val isClipboardDetectionEnabled by settingsManager.isClipboardDetectionEnabled.collectAsState(initial = true)
                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(MaterialTheme.shapes.medium)
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.ContentPaste,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "Clipboard URL Detection",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = "Auto-detect URLs in clipboard when app opens",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        Switch(
                            checked = isClipboardDetectionEnabled,
                            onCheckedChange = { enabled ->
                                scope.launch {
                                    settingsManager.setClipboardDetectionEnabled(enabled)
                                }
                            },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = MaterialTheme.colorScheme.primary,
                                checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                                uncheckedThumbColor = MaterialTheme.colorScheme.onSurfaceVariant,
                                uncheckedTrackColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                            )
                        )
                    }
                }



                // Advanced Settings Section Header with some spacing
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "Advanced Settings",
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                )

                // Battery Optimization Setting
                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(MaterialTheme.shapes.medium)
                        .clickable {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                val intent = Intent(
                                    Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS,
                                    Uri.parse("package:${context.packageName}")
                                )
                                context.startActivity(intent)
                            }
                        }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.PowerSettingsNew,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "Battery Optimization",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = "Disable battery optimization for reliable downloads",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        Icon(
                            Icons.Default.ChevronRight,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // Reset Database Setting
                var showResetDatabaseDialog by remember { mutableStateOf(false) }

                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(MaterialTheme.shapes.medium)
                        .clickable { showResetDatabaseDialog = true }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.error
                            )
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "Reset Database",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = "Fix database errors by resetting it (will clear all downloads)",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        Icon(
                            Icons.Default.ChevronRight,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                // Refresh App Icon option
                ElevatedCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(MaterialTheme.shapes.medium)
                        .clickable {
                            refreshAppIcon(context)
                        }
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier.weight(1f),
                            horizontalArrangement = Arrangement.spacedBy(16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Refresh,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "Refresh App Icon",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )
                                Text(
                                    text = "Clear launcher's icon cache for updated app icon",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                        Icon(
                            Icons.Default.ChevronRight,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }



                if (showResetDatabaseDialog) {
                    AlertDialog(
                        onDismissRequest = { showResetDatabaseDialog = false },
                        containerColor = MaterialTheme.colorScheme.surfaceVariant,
                        titleContentColor = MaterialTheme.colorScheme.onSurfaceVariant,
                        textContentColor = MaterialTheme.colorScheme.onSurfaceVariant,
                        title = { Text("Reset Database") },
                        text = {
                            Text("This will clear all download history and reset the database. This action cannot be undone. Continue?")
                        },
                        confirmButton = {
                            TextButton(
                                onClick = {
                                    // Reset database
                                    com.akira.manager.data.local.DownloadDatabase.resetDatabase(context)
                                    android.widget.Toast.makeText(
                                        context,
                                        "Database reset successful. App will restart.",
                                        android.widget.Toast.LENGTH_LONG
                                    ).show()
                                    showResetDatabaseDialog = false

                                    // Restart the app
                                    val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
                                    intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                                    intent?.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.startActivity(intent)
                                    (context as? Activity)?.finish()
                                }
                            ) {
                                Text("Reset", color = MaterialTheme.colorScheme.error)
                            }
                        },
                        dismissButton = {
                            TextButton(onClick = { showResetDatabaseDialog = false }) {
                                Text("Cancel")
                            }
                        }
                    )
                }

                // Footer with Made with Love text - Moved inside Column
                Spacer(modifier = Modifier.height(24.dp)) // Add some space before the footer
                Row(
                    modifier = Modifier
                        .fillMaxWidth() // Removed align modifier
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Made with ",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Icon(
                        imageVector = Icons.Default.Favorite,
                        contentDescription = null,
                        modifier = Modifier
                            .size(16.dp)
                            .padding(horizontal = 2.dp),
                        tint = MaterialTheme.colorScheme.error
                    )
                    Text(
                        text = " by ",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "akiralab",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            color = MaterialTheme.colorScheme.primary
                        ),
                        modifier = Modifier
                            .clickable {
                                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(
                                    "https://play.google.com/store/apps/dev?id=6442565546385690850"
                                ))
                                context.startActivity(intent)
                            }
                            .padding(horizontal = 4.dp)
                    )
                }
            } // Column ends here
        } // Box ends here
    } // Scaffold lambda ends here

    // Show dialog for download location selection
    if (showPathDialog) {
        AlertDialog(
            onDismissRequest = { showPathDialog = false },
            title = { Text("Download Location") },
            text = { 
                Column {
                    Text("Current location: $displayPath")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("Select a folder where downloaded files will be saved.")
                }
            },
            confirmButton = {
                TextButton(onClick = {
                    showPathDialog = false
                    directoryPicker.launch(null)
                }) {
                    Text("Select New Location")
                }
            },
            dismissButton = {
                Row {
                    TextButton(
                        onClick = { 
                            showPathDialog = false 
                            // Reset to default path
                            customPath = ""
                            settingsManager.setCustomDownloadPath("")
                            
                            // Show confirmation
                            android.widget.Toast.makeText(
                                context,
                                "Reset to default download location",
                                android.widget.Toast.LENGTH_SHORT
                            ).show()
                        }
                    ) {
                        Text("Reset to Default")
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                    TextButton(onClick = { showPathDialog = false }) {
                        Text("Cancel")
                    }
                }
            }
        )
    }
} // End of SettingsScreen composable

// Helper function to get a user-friendly display name from a path
private fun getDisplayNameFromPath(path: String): String {
    return when {
        path.isEmpty() -> "Default (Downloads/MDM)"
        path.contains("/storage/emulated/0/") -> {
            val afterStorage = path.substringAfter("/storage/emulated/0/")
            "Internal storage/$afterStorage"
        }
        path.contains("/storage/") -> {
            val afterStorage = path.substringAfter("/storage/")
            "Storage/$afterStorage"
        }
        path.startsWith("content://") -> {
            val lastSegment = Uri.parse(path).lastPathSegment ?: "Selected folder"
            lastSegment
        }
        else -> path
    }
}

// Function to refresh app icon by clearing launcher's icon cache
private fun refreshAppIcon(context: android.content.Context) {
    try {
        // Get the launcher intent for the app
        val packageManager = context.packageManager
        val intent = packageManager.getLaunchIntentForPackage(context.packageName)

        // Force a cache clear using various methods
        if (intent != null) {
            // Request the package manager to update the app's shortcut
            val componentName = intent.component
            if (componentName != null) {
                packageManager.setComponentEnabledSetting(
                    componentName,
                    android.content.pm.PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                    android.content.pm.PackageManager.DONT_KILL_APP
                )

                packageManager.setComponentEnabledSetting(
                    componentName,
                    android.content.pm.PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                    android.content.pm.PackageManager.DONT_KILL_APP
                )

                // Show success message
                android.widget.Toast.makeText(
                    context,
                    "App icon cache cleared",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }
    } catch (e: Exception) {
        // Show error message
        android.widget.Toast.makeText(
            context,
            "Failed to refresh app icon: ${e.message}",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }
}
