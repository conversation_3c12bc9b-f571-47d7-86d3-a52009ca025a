<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.MaterialManager" parent="android:Theme.Material.NoActionBar">
        <item name="android:windowBackground">@android:color/system_neutral1_900</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <style name="Theme.MaterialManager.Browser" parent="Theme.MaterialManager">
        <item name="android:windowAnimationStyle">@style/BrowserActivityAnimation</item>
        <item name="android:windowBackground">@android:color/system_neutral1_900</item>
    </style>

    <style name="BrowserActivityAnimation" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right</item>
    </style>
</resources>
