<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">

    <shortcut
        android:shortcutId="add_download"
        android:enabled="true"
        android:icon="@drawable/ic_download"
        android:shortcutShortLabel="@string/shortcut_download_short"
        android:shortcutLongLabel="@string/shortcut_download_long">
        <intent
            android:action="android.intent.action.MAIN"
            android:targetPackage="com.akira.mdm"
            android:targetClass="com.akira.manager.MainActivity">
            <extra android:name="route" android:value="main" />
            <extra android:name="action" android:value="download" />
        </intent>
        <capability android:name="actions.intent.START_DOWNLOAD">
            <parameter
                android:name="feature"
                android:key="download" />
        </capability>
    </shortcut>

    <shortcut
        android:shortcutId="open_browser"
        android:enabled="true"
        android:icon="@drawable/ic_browser"
        android:shortcutShortLabel="@string/shortcut_browser_short"
        android:shortcutLongLabel="@string/shortcut_browser_long">
        <intent
            android:action="android.intent.action.MAIN"
            android:targetPackage="com.akira.mdm"
            android:targetClass="com.akira.manager.MainActivity">
            <extra android:name="route" android:value="main" />
            <extra android:name="action" android:value="browser" />
        </intent>
        <capability android:name="actions.intent.OPEN_APP_FEATURE">
            <parameter
                android:name="feature"
                android:key="browser" />
        </capability>
    </shortcut>
</shortcuts>